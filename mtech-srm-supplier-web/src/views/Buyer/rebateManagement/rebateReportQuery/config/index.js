import { i18n } from '@/main.js'
import dayjs from 'dayjs'
import { formatAmount } from '@/utils/utils'
import { getCurrentBu } from '@/constants/bu'

// 状态列表
export const statusList = [
  { text: i18n.t('新建'), value: 1, cssClass: '' },
  { text: i18n.t('返利协议供应商待确认'), value: 2, cssClass: '' },
  { text: i18n.t('返利协议供应商拒绝'), value: 3, cssClass: '' },
  { text: i18n.t('返利协议触发审批失败'), value: 4, cssClass: '' },
  { text: i18n.t('返利协议审批中'), value: 5, cssClass: '' },
  { text: i18n.t('返利协议审批拒绝'), value: 6, cssClass: '' },
  { text: i18n.t('返利协议审批废弃'), value: 7, cssClass: '' },
  { text: i18n.t('返利协议审批通过'), value: 8, cssClass: '' },
  { text: i18n.t('返利金额执行中'), value: 9, cssClass: '' },
  { text: i18n.t('待供应商维护红字发票'), value: 20, cssClass: '' },
  { text: i18n.t('附件待采方审核'), value: 30, cssClass: '' },
  { text: i18n.t('附件采方审核驳回'), value: 40, cssClass: '' },
  { text: i18n.t('附件采方审核通过'), value: 50, cssClass: '' },
  { text: i18n.t('待采方上传协议盖章附件'), value: 60, cssClass: '' },
  { text: i18n.t('已结案'), value: 12, cssClass: '' }
]
// 金额行状态列表
export const amountItemStatusList = [
  { text: i18n.t('新建'), value: 1, cssClass: '' },
  { text: i18n.t('供应商待对账'), value: 7, cssClass: '' },
  { text: i18n.t('对账申请审批中'), value: 8, cssClass: '' },
  { text: i18n.t('待采方确认返利金额'), value: 30, cssClass: '' },
  { text: i18n.t('供应商待确认'), value: 2, cssClass: '' },
  { text: i18n.t('供应商已确认'), value: 3, cssClass: '' },
  { text: i18n.t('供应商已拒绝'), value: 4, cssClass: '' },
  { text: i18n.t('金额明细待审批'), value: 9, cssClass: '' },
  { text: i18n.t('金额明细审批中'), value: 10, cssClass: '' },
  { text: i18n.t('金额明细审批通过'), value: 11, cssClass: '' },
  { text: i18n.t('金额明细审批驳回'), value: 12, cssClass: '' },
  { text: i18n.t('红字开票OA审批中'), value: 40, cssClass: '' },
  { text: i18n.t('返利金额待采方上传附件'), value: 20, cssClass: '' },
  { text: i18n.t('推送成功'), value: 5, cssClass: '' },
  { text: i18n.t('推送失败'), value: 6, cssClass: '' }
]
// 返利计算频次
export const rebateFreqOptions = [
  { text: i18n.t('月度'), value: 1, cssClass: '' },
  { text: i18n.t('双月'), value: 2, cssClass: '' },
  { text: i18n.t('季度'), value: 3, cssClass: '' },
  { text: i18n.t('半季度'), value: 4, cssClass: '' },
  { text: i18n.t('年度'), value: 5, cssClass: '' },
  { text: i18n.t('一次返利'), value: 6, cssClass: '' }
]
// 返利类型枚举
export const rebateTypeList = [
  { text: i18n.t('物料类别采购金额*返利比例'), value: 1, cssClass: '' },
  { text: i18n.t('物料类别采购金额*阶梯返利（金额、比例)'), value: 2, cssClass: '' },
  { text: i18n.t('物料类别采购数量*阶梯返利(金额、比例)'), value: 3, cssClass: '' },
  { text: i18n.t('物编采购金额*返利比例'), value: 4, cssClass: '' },
  { text: i18n.t('物编采购数量*单位数量返利金额'), value: 5, cssClass: '' },
  { text: i18n.t('物编采购金额*阶梯返利(金额、比例)'), value: 6, cssClass: '' },
  { text: i18n.t('物编采购数量*阶梯返利(金额、比例、差价）'), value: 7, cssClass: '' },
  { text: i18n.t('其他-一次执行'), value: 8, cssClass: '' },
  { text: i18n.t('其他-分频次执行'), value: 9, cssClass: '' },
  { text: i18n.t('付现返利'), value: 10, cssClass: '' }
]

const columnData = [
  {
    type: 'checkbox',
    width: 50,
    allowEditing: false,
    allowResizing: false,
    customAttributes: {
      class: 'sticky-col-0'
    }
  },
  {
    field: 'rebateCode',
    headerText: i18n.t('返利协议单号'),
    customAttributes: {
      class: 'sticky-col-1'
    }
  },
  {
    field: 'rebateName',
    headerText: i18n.t('返利协议名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司'),
    formatter: (column, row) => {
      return row.companyCode ? row.companyCode + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },
  {
    field: 'totalRound',
    headerText: i18n.t('返利总轮次')
  },
  {
    field: 'residualRound',
    headerText: i18n.t('剩余轮次')
  },
  {
    field: 'currentRound',
    headerText: i18n.t('返利当前轮次')
  },
  {
    field: 'rebateType',
    headerText: i18n.t('返利类型'),
    valueConverter: {
      type: 'map',
      map: rebateTypeList
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('物料类别')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('物料类别描述')
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'materialName',
    headerText: i18n.t('物料编码描述')
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    formatter: (column, row) => {
      return row.factoryCode ? row.factoryCode + '-' + row.factoryName : ''
    }
  },
  {
    field: 'startYear',
    headerText: i18n.t('返利起始年')
  },
  {
    field: 'startDate',
    headerText: i18n.t('返利起始日'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(row[field].valueOf()).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('返利结束日'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(row[field].valueOf()).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'rebateStartDate',
    headerText: i18n.t('返利时间从')
    // formatter: ({ field }, row) => {
    //   return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    // }
  },
  {
    field: 'rebateEndDate',
    headerText: i18n.t('返利时间至')
    // formatter: ({ field }, row) => {
    //   return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    // }
  },
  {
    field: 'rebateFreq',
    headerText: i18n.t('返利计算频次'),
    valueConverter: {
      type: 'map',
      map: rebateFreqOptions
    }
  },
  {
    field: 'actualStartDate',
    headerText: i18n.t('实际返利起始时间')
  },
  {
    field: 'untaxedAmt',
    headerText: i18n.t('返利金额（未税）'),
    formatter: (column, row) => {
      const val = formatAmount(row.untaxedAmt)
      return val
    }
  },
  {
    field: 'rebateRatio',
    headerText: i18n.t('返利比例（%）')
  },
  {
    field: 'priceDiff',
    headerText: i18n.t('单片差价')
  },
  {
    field: 'ladderInfo	',
    headerText: i18n.t('阶梯等级')
  },
  {
    field: 'payCashRatio',
    headerText: i18n.t('付现比例')
  },
  {
    field: 'settleQty',
    headerText: i18n.t('当前结算数量')
  },
  {
    field: 'settleAmt',
    headerText: i18n.t('当前结算金额'),
    formatter: (column, row) => {
      const val = formatAmount(row.settleAmt)
      return val
    }
  },
  {
    field: 'amtUntaxed',
    headerText: i18n.t('返利金额（不含税）'),
    formatter: (column, row) => {
      const val = formatAmount(row.amtUntaxed)
      return val
    }
  },
  {
    field: 'amtTaxed',
    headerText: i18n.t('返利金额（含税）'),
    formatter: (column, row) => {
      const val = formatAmount(row.amtTaxed)
      return val
    }
  },
  {
    field: 'amountItemStatus',
    headerText: i18n.t('金额行状态'),
    valueConverter: {
      type: 'map',
      map: amountItemStatusList
    }
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'currency',
    headerText: i18n.t('币种')
  },
  {
    field: 'profitCenter',
    headerText: i18n.t('利润中心')
  },
  {
    field: 'sourceCreatedDate',
    headerText: i18n.t('协议创建日期'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(row[field].valueOf()).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'amountItemCreateDate',
    headerText: i18n.t('金额创建日期'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(row[field].valueOf()).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'rebateHeaderCreator',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'fsscVoucherNumber',
    headerText: i18n.t('共享单号')
  },
  {
    field: 'pushFsscYear',
    headerText: i18n.t('推送共享年')
  },
  {
    field: 'pushShareTime',
    headerText: i18n.t('推送共享时间')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'groupingName',
    headerText: i18n.t('组别')
  },
  {
    field: 'medCateg',
    headerText: i18n.t('重分类中类')
  },
  {
    field: 'operateRecords',
    headerText: i18n.t('返利执行明细'),
    width: 150,
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: () => {
        return i18n.t('返利明细')
      }
    }
  },
  {
    field: 'rebateAmountItemId',
    headerText: i18n.t('对账申请审批单号')
  }
]

export const pageConfig = [
  {
    gridId: '82537b16-863f-4af8-800c-c6f51547ec2d',
    title: i18n.t('返利协议创建'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    isUseCustomEditor: true,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'viewOA', icon: 'icon_solid_Download', title: i18n.t('查看OA审批') },
          { id: 'reconApply', icon: 'icon_solid_Download', title: i18n.t('对账申请') },
          { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      editSettings: {
        allowEditing: false,
        allowAdding: false,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        showDeleteConfirmDialog: false
      },
      pageSettings: {
        pageSizes: [10, 20, 50, 100, 200, 500]
      },
      // customSelection: true,
      // showSelected: false,
      columnData,
      asyncConfig: {
        url: '/analysis/tenant/rebateRecord/list',
        params: {
          bu: getCurrentBu()
        }
      }
    }
  }
]
