import { i18n } from '@/main.js'
import dayjs from 'dayjs'
import { getCurrentBu } from '@/constants/bu'

// 状态列表
export const statusList = [
  { text: i18n.t('新建'), value: 1, cssClass: '' },
  { text: i18n.t('返利协议供应商待确认'), value: 2, cssClass: '' },
  { text: i18n.t('返利协议供应商拒绝'), value: 3, cssClass: '' },
  { text: i18n.t('返利协议触发审批失败'), value: 4, cssClass: '' },
  { text: i18n.t('返利协议审批中'), value: 5, cssClass: '' },
  { text: i18n.t('返利协议审批拒绝'), value: 6, cssClass: '' },
  { text: i18n.t('返利协议审批废弃'), value: 7, cssClass: '' },
  { text: i18n.t('返利协议审批通过'), value: 8, cssClass: '' },
  { text: i18n.t('返利金额执行中'), value: 9, cssClass: '' },
  { text: i18n.t('待供应商维护红字发票'), value: 20, cssClass: '' },
  { text: i18n.t('附件待采方审核'), value: 30, cssClass: '' },
  { text: i18n.t('附件采方审核驳回'), value: 40, cssClass: '' },
  { text: i18n.t('附件采方审核通过'), value: 50, cssClass: '' },
  { text: i18n.t('待采方上传协议盖章附件'), value: 60, cssClass: '' },
  { text: i18n.t('已结案'), value: 12, cssClass: '' }
]
// 处理意见列表
export const handleRemarkList = [
  { text: i18n.t('驳回'), value: '0', cssClass: '' },
  { text: i18n.t('同意'), value: '1', cssClass: '' }
]

const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   customAttributes: {
  //     class: 'sticky-col-0'
  //   }
  // },
  {
    field: 'rebateCode',
    headerText: i18n.t('返利协议单号'),
    cssClass: 'field-content'
    // customAttributes: {
    //   class: 'sticky-col-1'
    // }
  },
  {
    field: 'rebateName',
    headerText: i18n.t('返利协议名称')
  },
  {
    field: 'agreementTemplateName',
    headerText: i18n.t('返利协议模板')
  },
  {
    field: 'company',
    headerText: i18n.t('公司'),
    formatter: (column, row) => {
      return row.companyCode ? row.companyCode + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },
  {
    field: 'oaStatus',
    headerText: i18n.t('OA审批状态')
  },
  {
    field: 'startDate',
    headerText: i18n.t('返利起始日'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('返利结束日'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]

export const pageConfig = [
  {
    gridId: '82537b16-863f-4af8-800c-c6f51547ec2c',
    title: i18n.t('待推送'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [{ id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      columnData,
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      showSelected: false,
      asyncConfig: {
        url: '/analysis/tenant/rebateHeader/pageQuery',
        params: { queryType: 1, bu: getCurrentBu() }
      }
    }
  },
  {
    gridId: '68f5a23b-a6b7-48f0-aa1d-5909f9b89251',
    title: i18n.t('汇总'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [{ id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      columnData,
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      showSelected: false,
      asyncConfig: {
        url: '/analysis/tenant/rebateHeader/pageQuery',
        params: { queryType: 2, bu: getCurrentBu() }
      }
    }
  },
  {
    gridId: '488f27d9-03d6-49dd-b510-428e409c4c11',
    title: i18n.t('待提交'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: {
      useBaseConfig: false,
      tools: [[], ['Filter', 'Refresh', 'Setting']]
    },
    useToolTemplate: false,
    grid: {
      columnData,
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      showSelected: false,
      asyncConfig: {
        url: '/analysis/tenant/rebateHeader/pageQuery',
        params: { queryType: 3, bu: getCurrentBu() }
      }
    }
  }
]
