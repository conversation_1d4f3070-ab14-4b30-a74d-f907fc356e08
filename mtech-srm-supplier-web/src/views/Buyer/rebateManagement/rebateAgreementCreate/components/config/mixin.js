import dayjs from 'dayjs'
import { rebateTypeList, rebateTypePVList } from './index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { getRebateCalculationFrequencyList } from './index'

export default {
  components: { VxeRemoteSearch },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  watch: {
    list: {
      handler(val) {
        let tableData = val
        if (this.type === 'detail') {
          tableData.forEach((item) => {
            if (item.actualStartDate && !isNaN(Number(item.actualStartDate))) {
              tableData.stepList = item.ladderInfo ? JSON.parse(item.ladderInfo) : []
            }
          })
        }
        // this.type === 'detail' && (tableData = [...this.serializeList(val)])
        this.tableData = [...tableData]
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    editable() {
      return [1, 3, 6].includes(this.detailInfo.status)
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      const { id } = this.$route.query
      const currentBu = localStorage.getItem('currentBu')
      switch (this.type) {
        case 'detail':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: !this.editable
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: !this.editable
            },
            {
              code: 'import',
              name: this.$t('导入'),
              status: 'info',
              isHidden: currentBu === 'GF' || !id || !this.editable
            },
            {
              code: 'export',
              name: this.$t('导出'),
              status: 'info',
              isHidden: !id
            }
          ]
        case 'purcahseAttachment':
          return [
            {
              code: 'upload',
              name: this.$t('上传'),
              status: 'info'
              // isHidden: !this.editable
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info'
              // isHidden: !this.editable
            },
            {
              code: 'download',
              name: this.$t('下载'),
              status: 'info'
            }
          ]
        case 'supplierAttachment':
          return [
            {
              code: 'download',
              name: this.$t('下载'),
              status: 'info'
            }
          ]
        default:
          return []
      }
    },
    cellTools() {
      switch (this.type) {
        case 'purcahseAttachment':
          return ['download', 'delete']
        case 'supplierAttachment':
          return ['download']
        default:
          return []
      }
    },
    columns() {
      const currentBu = localStorage.getItem('currentBu')
      switch (this.type) {
        case 'detail':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'lineNumber',
              title: this.$t('行号'),
              width: 50
            },
            {
              field: 'categoryCode',
              title: this.$t('物料类别'),
              minWidth: 140,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const isStep = [1, 2, 3, 8, 9].includes(row.rebateType)
                  return [
                    <div>
                      <VxeRemoteSearch
                        v-show={isStep}
                        v-model={row.categoryCode}
                        fileds={{ value: 'categoryCode', text: 'categoryName' }}
                        request-info={{
                          urlPre: 'maintainOutsideItem',
                          url: 'criteriaQuery',
                          searchKey: 'fuzzyNameOrCode',
                          recordsPosition: 'data'
                        }}
                        on-change={(item) => {
                          row.categoryName = item?.categoryName || null
                        }}
                      />
                      <span v-show={!isStep}>{row.categoryCode}</span>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'categoryName',
              title: this.$t('物料类别名称'),
              minWidth: 140
            },
            {
              field: 'materialCode',
              title: this.$t('物料编码'),
              minWidth: 140,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const isStep = [4, 5, 6, 7].includes(row.rebateType)
                  return [
                    <VxeRemoteSearch
                      v-show={isStep}
                      v-model={row.materialCode}
                      fileds={{ value: 'itemCode', text: 'itemName' }}
                      request-info={{
                        urlPre: 'maintainOutsideItem',
                        url: 'fuzzyQuery',
                        searchKey: 'fuzzyNameOrCode',
                        recordsPosition: 'data'
                      }}
                      on-change={async (item) => {
                        row.materialName = item?.itemName || null

                        // 根据物料编码带出物料类别
                        if (item) {
                          const res = await this.$API.rebateManagement.getCategoryListByMaterial({
                            itemCodeList: [item.itemCode]
                          })
                          if (res.code === 200 && res.data?.length) {
                            const { categoryCode, categoryName } = res.data[0]
                            row.categoryCode = categoryCode
                            row.categoryName = categoryName
                          }
                        } else {
                          row.categoryCode = null
                          row.categoryName = null
                        }
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'materialName',
              title: this.$t('物料名称'),
              minWidth: 140
            },
            {
              field: 'rebateType',
              title: this.$t('返利类型'),
              minWidth: 280,
              editRender: {},
              slots: {
                default: ({ row }) => {
                  const currentBu = localStorage.getItem('currentBu')
                  const typeList = currentBu === 'GF' ? rebateTypePVList : rebateTypeList
                  const selectedItem = typeList.find((item) => item.value === row.rebateType)
                  const rebateTypeName = selectedItem?.text || null
                  return [<span>{rebateTypeName}</span>]
                },
                edit: ({ row }) => {
                  const currentBu = localStorage.getItem('currentBu')
                  let typeList = currentBu === 'GF' ? rebateTypePVList : rebateTypeList

                  // 当协议类型是专案返利时，返利类型只能选择其他-一次执行
                  if (this.detailInfo && this.detailInfo.agreementType === 1) {
                    typeList = typeList.filter(item => item.value === 8)
                  }

                  return [
                    <vxe-select
                      v-model={row.rebateType}
                      clearable
                      options={typeList}
                      option-props={{ label: 'text', value: 'value' }}
                      transfer
                      on-change={(item) => {
                        if (item.value === 8) {
                          row.rebateFreq = 6
                        } else {
                          row.rebateFreq = null
                        }
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'untaxedAmt',
              title: currentBu === 'GF' ? this.$t('返利金额（含税）') : this.$t('返利金额（未税）'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const isStep = [8, 9].includes(row.rebateType)
                  return [
                    <vxe-input
                      v-show={isStep}
                      v-model={row.untaxedAmt}
                      clearable
                      type='number'
                      min='0'
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'rebateRatio',
              title: this.$t('返利比例（%）'),
              minWidth: 140,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const isStep = [1, 4].includes(row.rebateType)
                  return [
                    <vxe-input
                      v-show={isStep}
                      v-model={row.rebateRatio}
                      clearable
                      type='number'
                      min='0'
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'priceDiff',
              title: this.$t('单片差价'),
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const isStep = [5].includes(row.rebateType)
                  return [
                    <vxe-input
                      v-show={isStep}
                      v-model={row.priceDiff}
                      clearable
                      type='number'
                      min='0'
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'stepRank',
              title: this.$t('阶梯等级'),
              slots: {
                default: ({ row, column }) => {
                  const isStep = [2, 3, 6, 7].includes(row.rebateType)
                  return [
                    <div>
                      <a
                        v-show={isStep}
                        style='color: #409eff'
                        on-click={() => this.handleClickCellTitle(row, column)}>
                        {this.$t('阶梯等级')}
                      </a>
                      <span v-show={!isStep}>-</span>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'payCashRatio',
              title: this.$t('付现比例'),
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const isEdit = [10].includes(row.rebateType)
                  return [
                    <vxe-input
                      v-show={isEdit}
                      v-model={row.payCashRatio}
                      clearable
                      type='number'
                      min='0'
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'rebateFreq',
              title: this.$t('返利计算频次'),
              minWidth: 140,
              editRender: {},
              slots: {
                default: ({ row }) => {
                  const frequencyList = getRebateCalculationFrequencyList()
                  const selectedItem = frequencyList.find((item) => item.value === row.rebateFreq)
                  const rebateFreqName = selectedItem?.text || null
                  return [<span>{rebateFreqName}</span>]
                },
                edit: ({ row }) => {
                  const frequencyList = getRebateCalculationFrequencyList()
                  return [
                    <vxe-select
                      v-model={row.rebateFreq}
                      clearable
                      options={frequencyList}
                      option-props={{ label: 'text', value: 'value' }}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'actualStartDate',
              title: this.$t('实际返利起始时间'),
              minWidth: 160,
              editRender: {},
              slots: {
                default: ({ row }) => {
                  const actualStartDate = Number(row.actualStartDate)
                    ? dayjs(Number(row.actualStartDate)).format('YYYY-MM-DD')
                    : row.actualStartDate
                  return [<span>{actualStartDate}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.actualStartDate}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={this.disabledDateMethod}
                    />
                  ]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 140,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [<vxe-input v-model={row.remark} clearable maxlength='200' />]
                }
              }
            }
          ]
        case 'purcahseAttachment':
        case 'supplierAttachment':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              title: this.$t('序号'),
              width: 50,
              type: 'seq'
            },
            {
              field: 'attachmentName',
              title: this.$t('附件名称'),
              slots: {
                default: ({ row, column }) => {
                  return [
                    <div>
                      <a
                        style='display: block; color: #409eff'
                        on-click={() => this.handleClickCellTitle(row, column)}>
                        {row.attachmentName}
                      </a>
                      <a
                        class='cell-btn'
                        v-show={this.cellTools.includes('download')}
                        on-click={() => this.handleClickCellTool('download', row)}>
                        <i class='vxe-icon-download' />
                        {this.$t('下载')}
                      </a>
                      <a
                        class='cell-btn'
                        v-show={this.cellTools.includes('delete')}
                        on-click={() => this.handleClickCellTool('delete', row)}>
                        <i class='vxe-icon-delete' />
                        {this.$t('删除')}
                      </a>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'attachmentSize',
              title: this.$t('附件大小'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>{Number(((row.attachmentSize / 1024) * 100) / 100).toFixed(2)}KB</span>
                  ]
                }
              }
            },
            {
              field: 'uploadUserName',
              title: this.$t('上传人')
            },
            {
              field: 'uploadTime',
              title: this.$t('上传时间'),
              slots: {
                default: ({ row }) => {
                  const uploadTime = row.uploadTime
                    ? dayjs(Number(row.uploadTime)).format('YYYY-MM-DD HH:mm:ss')
                    : null
                  return [<span>{uploadTime}</span>]
                }
              }
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {},
  methods: {
    // 限制日期选择
    disabledDateMethod(e) {
      const { date } = e
      const day = dayjs(date).get('date')
      const currentBu = localStorage.getItem('currentBu')

      // 空调事业部只能选择当前日期往后的每月23号，其他事业部选择每月5号
      const allowedDay = currentBu === 'KT' ? 23 : 5

      return Date.now() - 86400000 >= new Date(date).getTime() || day !== allowedDay
    },
    // 序列化列表
    serializeList(list) {
      list.forEach((item) => {
        if (item.actualStartDate && !isNaN(Number(item.actualStartDate))) {
          item.stepList = item.ladderInfo ? JSON.parse(item.ladderInfo) : []
        }
      })
      return list
    }
  }
}
