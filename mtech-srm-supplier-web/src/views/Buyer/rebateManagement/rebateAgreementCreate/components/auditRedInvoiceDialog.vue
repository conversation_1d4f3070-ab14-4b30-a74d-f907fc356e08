<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formRef" :model="formData" :rules="rules">
        <mt-form-item prop="auditResult" :label="$t('审核结果')" label-style="top">
          <mt-select
            v-model="formData.auditResult"
            :data-source="auditResultOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择审核结果')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        auditResult: ''
      },
      auditResultOptions: [
        {
          text: this.$t('通过'),
          value: 1
        },
        {
          text: this.$t('驳回'),
          value: 0
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title || this.$t('审核红字开票')
    },
    rules() {
      return {
        auditResult: [
          { required: true, message: this.$t('请选择审核结果'), trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formData)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px;

  .mt-form-item {
    margin-bottom: 20px;
  }
}
</style>
