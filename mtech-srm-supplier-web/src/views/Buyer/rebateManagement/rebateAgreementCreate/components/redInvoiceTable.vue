<template>
  <div class="red-invoice-table">
    <div class="table-item">
      <div>
        <sc-table
          ref="redInvoiceTableRef"
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  name: 'RedInvoiceTable',
  components: { ScTable },
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    redInvoiceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.redInvoiceTableRef?.$refs?.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'sellerName',
          title: this.$t('销售方名称'),
          minWidth: 200
        },
        {
          field: 'sellerTaxNumber',
          title: this.$t('销售方税号'),
          minWidth: 150
        },
        {
          field: 'buyerName',
          title: this.$t('购买方名称'),
          minWidth: 200
        },
        {
          field: 'buyerTaxNumber',
          title: this.$t('购买方税号'),
          minWidth: 150
        },
        {
          field: 'productName',
          title: this.$t('商品名称'),
          minWidth: 150
        },
        {
          field: 'productTaxItem',
          title: this.$t('商品税目'),
          minWidth: 120
        },
        {
          field: 'limitAmount',
          title: this.$t('开票含税金额'),
          minWidth: 130
        }
      ]
    }
  },
  mounted() {
    this.initTableData()
  },
  watch: {
    // 监听redInvoiceList变化，更新表格数据
    redInvoiceList: {
      handler(newVal) {
        this.loadInterfaceData(newVal || [])
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化表格数据
    initTableData() {
      // 使用props传入的数据初始化表格
      this.loadInterfaceData(this.redInvoiceList || [])
    },
    // 加载接口返回的数据
    loadInterfaceData(data) {
      this.tableData = data || []
      this.$nextTick(() => {
        if (this.tableRef) {
          this.tableRef.loadData(this.tableData)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.red-invoice-table {
  .table-item {
    width: 100%;
    margin-top: 10px;

    .item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item-title {
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 500;

        .title-prefix {
          width: 5px;
          height: 18px;
          background-color: #409eff;
          margin: 7px 15px 0 0;
        }
      }

      .item-icon {
        color: #409eff;
        transform: rotate(270deg);
        cursor: pointer;
      }

      .item-icon-hidden {
        transform: rotate(90deg);
        margin-right: 16px;
      }
    }
  }
}
</style>
