import { i18n } from '@/main.js'
import dayjs from 'dayjs'
import { getCurrentBu } from '@/constants/bu'

// 状态列表
export const statusList = [
  { text: i18n.t('新建'), value: 1, cssClass: '' },
  { text: i18n.t('返利协议供应商待确认'), value: 2, cssClass: '' },
  { text: i18n.t('返利协议供应商拒绝'), value: 3, cssClass: '' },
  { text: i18n.t('返利协议触发审批失败'), value: 4, cssClass: '' },
  { text: i18n.t('返利协议审批中'), value: 5, cssClass: '' },
  { text: i18n.t('返利协议审批拒绝'), value: 6, cssClass: '' },
  { text: i18n.t('返利协议审批废弃'), value: 7, cssClass: '' },
  { text: i18n.t('返利协议审批通过'), value: 8, cssClass: '' },
  { text: i18n.t('返利金额执行中'), value: 9, cssClass: '' },
  { text: i18n.t('待供应商维护红字发票'), value: 20, cssClass: '' },
  { text: i18n.t('附件待采方审核'), value: 30, cssClass: '' },
  { text: i18n.t('附件采方审核驳回'), value: 40, cssClass: '' },
  { text: i18n.t('附件采方审核通过'), value: 50, cssClass: '' },
  { text: i18n.t('待采方上传协议盖章附件'), value: 60, cssClass: '' },
  { text: i18n.t('已结案'), value: 12, cssClass: '' }
]
// 处理意见列表
export const handleRemarkList = [
  { text: i18n.t('驳回'), value: '0', cssClass: '' },
  { text: i18n.t('同意'), value: '1', cssClass: '' },
  { text: i18n.t('--'), value: null, cssClass: '' }
]

export const yesOrNoList = [
  { text: i18n.t('是'), value: 1, cssClass: '' },
  { text: i18n.t('否'), value: 0, cssClass: '' }
]

const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   customAttributes: {
  //     class: 'sticky-col-0'
  //   }
  // },
  {
    field: 'rebateCode',
    headerText: i18n.t('返利协议单号'),
    cssClass: 'field-content'
    // customAttributes: {
    //   class: 'sticky-col-1'
    // }
  },
  {
    field: 'rebateName',
    headerText: i18n.t('返利协议名称')
  },
  {
    field: 'agreementTemplateName',
    headerText: i18n.t('返利协议模板')
  },
  {
    field: 'company',
    headerText: i18n.t('所属公司'),
    formatter: (column, row) => {
      return row.companyCode ? row.companyCode + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },
  {
    field: 'startDate',
    headerText: i18n.t('返利起始日'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('返利结束日'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'currency',
    headerText: i18n.t('币种')
  },
  {
    field: 'costCurrency',
    headerText: i18n.t('本位币编码')
  },
  {
    field: 'feedbackDate',
    headerText: i18n.t('要求反馈日期'),
    formatter: ({ field }, row) => {
      return row[field] ? dayjs(Number(row[field])).format('YYYY-MM-DD') : ''
    }
  },
  {
    field: 'needOa',
    headerText: i18n.t('是否需要OA线上审核'),
    formatter: ({ cellValue }) => {
      return yesOrNoList.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    formatter: ({ field }, row) => {
      const dateObject = new Date(row[field])
      const year = dateObject.getFullYear()
      const month = dateObject.getMonth() + 1
      const day = dateObject.getDate()
      const formattedDate = `${year}-${month < 10 ? '0' + month : month}-${
        day < 10 ? '0' + day : day
      }`
      return formattedDate
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'flag',
    headerText: i18n.t('旧系统单号')
  },
  {
    field: 'supplierHandleRemark',
    headerText: i18n.t('供方处理意见'),
    valueConverter: {
      type: 'map',
      map: handleRemarkList
    }
  },
  {
    field: 'supplierFeedbackRemark',
    headerText: i18n.t('供方反馈意见')
  },
  {
    field: 'operateRecords',
    headerText: i18n.t('操作记录'),
    width: 100,
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: () => {
        return i18n.t('操作记录')
      }
    }
  }
]

export const pageConfig = [
  {
    gridId: '82537b16-863f-4af8-800c-c6f51547ec2b',
    title: i18n.t('返利协议创建'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    isUseCustomEditor: true,
    buttonQuantity: 8,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          {
            id: 'Create',
            icon: 'icon_solid_Createorder',
            title: i18n.t('新增')
          },
          'Edit',
          'Delete',
          {
            id: 'Submit',
            icon: 'icon_solid_submit',
            title: i18n.t('提交')
          },
          {
            id: 'Publish',
            icon: 'icon_card_invite',
            title: i18n.t('发布')
          },
          { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') },
          {
            id: 'Print',
            icon: 'icon_solid_edit',
            title: i18n.t('打印')
          },
          {
            id: 'audit',
            title: i18n.t('查看OA审批'),
            icon: 'icon_solid_editsvg'
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      editSettings: {
        allowEditing: false,
        allowAdding: false,
        allowDeleting: true,
        mode: 'Normal',
        showConfirmDialog: false,
        showDeleteConfirmDialog: false
      },
      columnData,
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      showSelected: false,
      asyncConfig: {
        url: '/analysis/tenant/rebateHeader/pageQuery',
        params: { queryType: 2, bu: getCurrentBu() }
      }
    }
  }
]
