<template>
  <div style="padding: 10px 15px; background-color: #fff">
    <!-- 顶部按钮 -->
    <div class="btnGroups">
      <icon-button :text="$t('保存')" @click="saveCompanyInfo(0)" />
      <icon-button type="primary" :text="$t('返回')" @click="goBack" />
    </div>
    <div>
      <!-- 基础信息选择 -->
      <mt-form ref="ruleForm" class="" :model="formData" :rules="rules">
        <mt-row :gutter="12">
          <mt-col :span="6">
            <mt-form-item
              prop="companyCode"
              class="form-item"
              label-style="top"
              :label="$t('所属公司')"
            >
              <RemoteAutocomplete
                v-model="formData.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
                @change="orgChange"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="agreementType" :label="$t('协议类型')" label-style="top">
              <mt-select
                v-model="formData.agreementType"
                :data-source="agreementTypeOption"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择协议类型')"
                :open-dispatch-change="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              prop="agreementName"
              class="form-item"
              label-style="top"
              :label="$t('协议书名称')"
            >
              <mt-input
                maxlength="16"
                v-model="formData.agreementName"
                :show-clear-button="true"
                :disabled="false"
                :placeholder="$t('请输入')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input v-model="formData.remark"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <div class="edit-box">
        <rich-text-editor
          v-if="variableList.length"
          :toolbar="clientToolbar"
          :enable-resize="true"
          :height="700"
          ref="editor"
          v-model="templateText"
          :variable-list="variableList"
        ></rich-text-editor>
      </div>
    </div>
  </div>
</template>
<script>
import iconButton from '@/components/iconButton/index.vue'
import utils from '@/utils/utils'
import { defaultTemp } from './config.js'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    iconButton,
    RichTextEditor,
    RemoteAutocomplete
  },
  data() {
    const deadStockFactory = utils.getSupplierDict('IDLE_MATERIAL_PROFIT_FACTORY_MAP') || []
    return {
      variableList: [],
      clientToolbar: `bold italic underline | formatselect alignleft aligncenter alignright alignjustify bullist numlist | table link image backcolor | code undo redo paste | lineheight| parmarsSetButton |`,
      deadStockFactory,
      templateText: '', // 富文本value值
      formData: {
        remark: '',
        agreementName: '',
        agreementType: '',
        companyCode: '',
        templateText: '' // 模板富文本
      },
      // 下拉枚举值
      agreementTypeOption: [
        { text: this.$t('返利协议'), value: 1 },
        { text: this.$t('付现协议'), value: 2 }
      ],
      companyList: [],

      headerId: '',
      isReload: true,
      insertImageSettings: { saveFormat: 'Base64' },
      // toolbarSettings: {
      //   items: [
      //     {
      //       tooltipText: 'Insert Symbol',
      //       undo: true,
      //       click: this.onClick.bind(this),
      //       template: function () {
      //         return {
      //           template: Vue.component('actionOption', {
      //             template: `<div><span style="color:red">*</span><span>变量库：</span><mt-select v-model="varValue" :dataSource="variableList" id="custom_tbar" :fields="{ value: 'itemCode', text: 'itemName' }" placeholder="请选择"  @select="insertVar" style="width:auto"></mt-select></div>`,
      //             data() {
      //               return {
      //                 data: {},
      //                 variableList: [],
      //                 varValue: ''
      //               }
      //             },
      //             mounted() {
      //               this.$API.masterData
      //                 .dictionaryGetList({
      //                   dictCode: 'REBATE_AGREEMENT_TEMPLATE_VARIABLE'
      //                 })
      //                 .then((res) => {
      //                   this.variableList = res.data
      //                 })
      //             },
      //             computed: {},
      //             methods: {
      //               insertVar(e) {
      //                 console.log('insertVar==', e, this.data, proxy)
      //                 proxy.$refs.editorRef.ejsRef.executeCommand(
      //                   'insertText',
      //                   '${' + e.itemData.itemCode + '}',
      //                   {
      //                     undo: true
      //                   }
      //                 )
      //               }
      //             }
      //           })
      //         }
      //       }
      //     },
      //     'insertCode',
      //     'Bold',
      //     'Italic',
      //     'Underline',
      //     'StrikeThrough',
      //     'FontName',
      //     'FontSize',
      //     'FontColor',
      //     'BackgroundColor',
      //     'LowerCase',
      //     'UpperCase',
      //     'SuperScript',
      //     'SubScript',
      //     '|',
      //     'Formats',
      //     'Alignments',
      //     'NumberFormatList',
      //     'BulletFormatList',
      //     'Outdent',
      //     'Indent',
      //     '|',
      //     'CreateTable',
      //     'CreateLink',
      //     'Image',
      //     'FileManager',
      //     '|',
      //     'ClearFormat',
      //     'Print',
      //     'SourceCode',
      //     'FullScreen',
      //     '|',
      //     'Undo',
      //     'Redo'
      //   ]
      // },
      // quickToolbarSettings: {
      //   table: [
      //     'TableHeader',
      //     'TableRows',
      //     'TableColumns',
      //     'TableCell',
      //     '-',
      //     'BackgroundColor',
      //     'TableRemove',
      //     'TableCellVerticalAlign',
      //     'Styles'
      //   ]
      // },
      rules: {
        agreementName: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        agreementType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.$API.masterData
      .dictionaryGetList({
        dictCode: 'REBATE_AGREEMENT_TEMPLATE_VARIABLE'
      })
      .then((res) => {
        this.variableList = res.data.map(function (item) {
          return {
            type: 'choiceitem',
            text: item.itemName,
            value: item.itemCode
          }
        })
        sessionStorage.setItem('variableList', JSON.stringify(this.variableList))
      })
    this.init()
  },
  mounted() {},
  methods: {
    onCreate() {
      console.log('onCreate===')
    },
    onClick() {
      console.log('onClick==', this.$refs.editorRef)
    },
    init() {
      // 新增时： 富文本为前端传入
      if (!this.$route.query.id) {
        this.templateText = defaultTemp
      }
      // 编辑时：获取详细数据信息
      if (this.$route.query.id && sessionStorage.getItem('agreementTem')) {
        const formData = JSON.parse(sessionStorage.getItem('agreementTem'))
        this.formData = {
          ...formData,
          companyName: formData.companyName
        }
        this.templateText = formData.templateText
      }
    },
    // 保存接口
    saveCompanyInfo() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存数据？')
            },
            success: () => {
              this.formData.templateText = this.templateText
              const params = { ...this.formData }
              if (this.$route.query.id) {
                params.id = this.$route.query.id
              }
              this.getSaveHeaderApi()(params).then((res) => {
                const { code } = res
                if (code === 200) {
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                  this.$router.push({
                    path: '/supplier/rebate-management/rebate-agreement-tem'
                  })
                }
              })
            }
          })
        }
      })
    },
    getSaveHeaderApi() {
      return this.$API.rebateManagement.saveRebateTemplate
    },
    orgChange(e) {
      const { itemData } = e
      this.formData.companyName = itemData.orgName
    },
    getSaveItemApi(data) {
      if (data.id) {
        return this.$API.deadMaterials.updateItem
      } else {
        return this.$API.deadMaterials.addItem
      }
    },
    goBack() {
      this.$router.push({
        name: 'rebate-agreement-tem'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.edit-box {
  width: 100%;
  background: #fff;
  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }
  .rich-editor {
    height: 100%;
  }
  .mt-rich-text-editor {
    height: 100%;
  }
}
.btnGroups {
  display: flex;
  justify-content: flex-end;
  margin-left: 10px;
}
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
.detailData {
  height: 30px;
  line-height: 30px;
  border-left: 8px solid #00469c;
  padding-left: 10px;
  border-radius: 4px 0 0 4px;
}
.rinput {
  margin-left: 10px;
  // font-size: 0.18rem;
  height: 1.95rem;
  // width: 5.25rem;
  // position: absolute;
  // left: 0px;
}
.list-item2 {
  padding-left: 10px;
  line-height: 33px;
}
.list-item2:hover {
  background-color: #eeeeee;
}
.my-dropdown2 {
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
::v-deep .vxe-pulldown {
  width: 100%;
  .vxe-input {
    width: 100%;
  }
}
::v-deep .vxe-input--inner {
  width: 100%;
  height: 100%;
  border-radius: 0px;
  outline: 0;
  margin: 0;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  padding: 0 0.6em;
  color: #606266;
  border: 1px solid rgba(0, 0, 0, 0.6);
  border-top: none;
  border-left: none;
  border-right: none;
  background-color: #fff;
  box-shadow: none;
}
</style>
