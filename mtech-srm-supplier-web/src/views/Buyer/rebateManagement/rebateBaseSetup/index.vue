<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="searchFormModel.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="bookkeepingCode" :label="$t('记账码')" label-style="top">
              <mt-select
                v-model="searchFormModel.bookkeepingCode"
                css-class="rule-element"
                :data-source="bookkeepingOption"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择本位币')"
              />
            </mt-form-item>

            <mt-form-item prop="origCurrencyCode" :label="$t('原币')" label-style="top">
              <mt-select
                v-model="searchFormModel.origCurrencyCode"
                :data-source="currencyList"
                :fields="{ text: 'text', value: 'currencyCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择原币')"
              />
            </mt-form-item>
            <mt-form-item prop="costCurrency" :label="$t('本位币')" label-style="top">
              <mt-select
                v-model="searchFormModel.costCurrency"
                :data-source="currencyList"
                :fields="{ text: 'text', value: 'currencyCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择本位币')"
              />
            </mt-form-item>
            <mt-form-item prop="accountingSubjectCode" :label="$t('会计科目')" label-style="top">
              <mt-input
                v-model="searchFormModel.accountingSubjectCode"
                :show-clear-button="true"
                :placeholder="$t('请输入会计科目')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.factoryCode"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :url="$API.masterData.getSiteListUrl"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="factoryAddress"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="profitCenterCode" :label="$t('利润中心')" label-style="top">
              <mt-input
                v-model="searchFormModel.profitCenterCode"
                :show-clear-button="true"
                :placeholder="$t('请输入利润中心')"
              />
            </mt-form-item>
            <mt-form-item prop="accountingType" :label="$t('会计类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.accountingType"
                css-class="rule-element"
                :data-source="accounteOption"
                :show-clear-button="true"
                filter-type="Contains"
                :placeholder="$t('请选择会计类型')"
              />
            </mt-form-item>
            <mt-form-item prop="paymentTerms" :label="$t('付款条件')" label-style="top">
              <mt-input
                v-model="searchFormModel.paymentTerms"
                :show-clear-button="true"
                :placeholder="$t('请输入付款条件')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig, statusList } from './config'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getCurrentBuUpper } from '@/constants/bu'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      accounteOption: [
        { text: this.$t('含税'), value: 'INCLUDED_TAX' },
        { text: this.$t('不含税'), value: 'NINCLUDED_TAX' },
        { text: this.$t('税'), value: 'TAX_MONEY' }
      ],
      bookkeepingOption: [
        { text: this.$t('21'), value: '21' },
        { text: this.$t('50'), value: '50' },
        { text: this.$t('40'), value: '40' }
      ],
      searchFormModel: {
        startDateRange: [],
        endDateRange: [],
        feedbackDateRange: [],
        creatDateRange: []
      },
      pageConfig,
      statusList,
      currencyList: []
    }
  },
  computed: {},
  mounted() {
    this.getCurrencyList()
    this.$refs.templateRef.refreshCurrentGridData()
  },
  methods: {
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        const start = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        const end = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
        this.searchFormModel[field + 'Range'] = [new Date(start).getTime(), new Date(end).getTime()]
      } else {
        this.searchFormModel[field + 'Range'] = []
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'Enable' || e.tool.id == 'Disable') {
        this.$API.rebateManagement
          .updateStatus({
            id: e.data.id,
            status: e.tool.id == 'Enable' ? 'Y' : 'N'
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.gridRef.getCustomSelectedRows()
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleDelete(records)
      }
    },
    handleAdd() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },

    // 删除
    handleDelete(list) {
      if (list?.length !== 1) {
        const tip = list.length === 0 ? this.$t('请先选择一行') : this.$t('只能选择一行')
        this.$toast({ content: tip, type: 'warning' })
        return
      }
      const ids = []
      list.forEach((item) => ids.push(item.id))
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$loading()
          this.$API.rebateManagement
            .deleteRebateBaseSetup({ id: ids[0] })
            .then(() => {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    actionBegin(args) {
      const { requestType, data, rowData } = args
      if (requestType === 'beginEdit') {
        if (rowData.enableFlag == 'Y' || rowData.enableFlag == '') {
          args.cancel = true
          this.$refs.templateRef.refreshCurrentGridData()
        }
      } else if (requestType === 'save') {
        const validateMap = {
          autoCalcIndexType: {
            value: data.companyCode,
            msg: this.$t('请选择公司代码')
          },
          bookkeepingCode: {
            value: data.bookkeepingCode,
            msg: this.$t('请选择记账码')
          },
          origCurrencyCode: {
            value: data.origCurrencyCode,
            msg: this.$t('请选择原币')
          },
          costCurrency: {
            value: data.costCurrency,
            msg: this.$t('请选择本位币')
          },
          factoryCode: {
            value: data.factoryCode,
            msg: this.$t('请选择工厂代码')
          },
          profitCenterCode: {
            value: data.profitCenterCode,
            msg: this.$t('请选择利润中心')
          }
        }

        // 只有空调事业部才需要校验购买方税号
        if (['KT'].includes(getCurrentBuUpper())) {
          validateMap.taxNumber = {
            value: data.taxNumber,
            msg: this.$t('请输入购买方税号')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, action, data, rowIndex } = args
      if (requestType === 'save') {
        if (action === 'add') {
          data.id = null
        }
        this.handleSave(data, rowIndex)
      }
    },
    // 保存
    handleSave(rowData, rowIndex = 0) {
      this.$API.rebateManagement
        .rebateAddOrUpdate(rowData)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          }
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
}

/deep/.e-rowcell.sticky-col-0,
/deep/.e-headercell.sticky-col-0 {
  @include sticky-col;
  left: 0px;
}
/deep/.e-rowcell.sticky-col-1,
/deep/.e-headercell.sticky-col-1 {
  @include sticky-col;
  left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
}
::v-deep {
  .template_checkbox {
    text-align: center;
    input[type='checkbox'] {
      visibility: visible;
    }
  }
}
</style>
