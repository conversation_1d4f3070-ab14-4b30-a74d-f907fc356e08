<template>
  <div class="supplier-effective-apply">
    <div class="detail-header--wrap">
      <p class="detail-header-name">
        {{ effectiveData.supplierEnterpriseName }}

        <span class="detail-header-button--wrap">
          <mt-button type="text" class="detail-header-button" @click="backDetail">{{
            $t('返回')
          }}</mt-button>
          <mt-button v-if="isEdit" type="text" class="detail-header-button" @click="saveDetail">{{
            $t('保存')
          }}</mt-button>
          <mt-button
            v-if="isEdit && formObject.infoDTO.applyStatus != 90"
            type="text"
            class="detail-header-button"
            @click="saveAndSubmitDetail"
            >{{ $t('保存并提交') }}</mt-button
          >
        </span>
      </p>
      <p class="detail-header-category">{{ $t('品类：') }}{{ effectiveData.categoryName }}</p>
      <p class="detail-header-items">
        <span class="detail-header-item">{{ $t('公司：') }}{{ effectiveData.orgName }}</span>
        <span class="detail-header-item"
          >{{ $t('生效类型：')
          }}{{
            effectiveData.effectiveType === '1' ? $t('预生效（SAP）') : $t('正式生效（SRM）')
          }}</span
        >
        <span class="detail-header-item"
          >{{ $t('引入场景：') }}{{ effectiveData.sceneDefineName }}</span
        >
        <span class="detail-header-item"
          >{{ $t('生效方式：') }}{{ effectiveMethodMap[effectiveData.effectiveMethod] }}</span
        >
      </p>
    </div>
    <div class="detail-content--wrap">
      <mt-form
        ref="effectiveOrgDTO"
        class="detail-effectiveorg--form"
        :model="effectiveOrgData"
        :rules="rules"
      >
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="supplierCharacter" :label="$t('供应商特性')">
              <mt-select
                v-model="effectiveOrgData.supplierCharacter"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, supplierCharacterList, 'text')"
                :data-source="supplierCharacterList"
                :placeholder="$t('请选择供应商特性')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="supplierCharacterChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6" v-if="['2M01', '2S06'].includes(effectiveData.orgCode)">
            <mt-form-item prop="multiplePayCondition" :label="$t('是否多种付款条件')">
              <mt-radio
                v-model="effectiveOrgData.multiplePayCondition"
                :data-source="multiplePayConditionList"
                :disabled="!isEdit || isDocumentNotEdit"
              />
            </mt-form-item>
          </mt-col>

          <mt-col :span="6" v-if="effectiveOrgData.supplierCharacter === '1'">
            <mt-form-item prop="tradePartnerCode" :label="$t('贸易伙伴')">
              <mt-select
                v-model="effectiveOrgData.tradePartnerCode"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, tradePartnerList, 'tradingPartnerName')"
                :data-source="tradePartnerList"
                :fields="{
                  value: 'tradingPartnerCode',
                  text: 'tradingPartnerName'
                }"
                :placeholder="$t('请选择贸易伙伴')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="tradePartnerChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6" v-if="effectiveOrgData.supplierCharacter === '1'">
            <mt-form-item prop="sapCode" :label="$t('SAP编码')">
              <mt-input
                :disabled="!isEdit || isDocumentNotEdit"
                v-model="effectiveOrgData.sapCode"
                :placeholder="$t('请输入SAP编码')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="paymentTerm" :label="$t('付款条件')">
              <mt-select
                v-model="effectiveOrgData.paymentTerm"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, paymentTermList, 'paymentTermsName')"
                :data-source="paymentTermList"
                :fields="{
                  value: 'paymentTermsCode',
                  text: 'paymentTermsName'
                }"
                :disabled="!isEdit || isDocumentNotEdit"
                :placeholder="$t('请选择付款条件')"
                @change="paymentTermsChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="subject" :label="$t('统驭科目')">
              <mt-select
                v-model="effectiveOrgData.subject"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, subjectList, 'itemName')"
                :data-source="subjectList"
                :fields="dictFields"
                :placeholder="$t('请选择统驭科目')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="subjectChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="cashGroup" :label="$t('现金管理组')">
              <mt-select
                v-model="effectiveOrgData.cashGroup"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, cashGroupList, 'itemName')"
                :data-source="cashGroupList"
                :fields="dictFields"
                :placeholder="$t('请选择现金管理组')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="cashGroupChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="accountGroup" :label="$t('账户组')">
              <mt-select
                v-model="effectiveOrgData.accountGroup"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, accountGroupList, 'itemName')"
                :data-source="accountGroupList"
                :fields="dictFields"
                :placeholder="$t('请选择账户组')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="accountGroupChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="paymentMode" :label="$t('付款方式')">
              <mt-select
                v-model="effectiveOrgData.paymentMode"
                :show-clear-button="true"
                float-label-type="Never"
                :data-source="paymentModeList"
                :fields="{
                  value: 'paymethodCode',
                  text: 'paymethodName'
                }"
                :placeholder="$t('请选择付款方式')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="paymentModeChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="subRange" :label="$t('子范围')">
              <mt-multi-select
                v-model="effectiveOrgData.subRange"
                float-label-type="Never"
                :allow-filtering="true"
                :filtering="(e) => filteringResource(e, subRangeList, 'itemName')"
                :data-source="subRangeList"
                :fields="dictFields"
                :placeholder="$t('请选择子范围')"
                :disabled="!isEdit || isDocumentNotEdit"
                @change="subRangeChange"
              ></mt-multi-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <mt-form
        ref="effectiveDTO"
        class="detail-effectiveorg--form"
        :model="effectiveData"
        :rules="effectiveDTORules"
      >
        <mt-form-item prop="reason" :label="$t('原因说明')">
          <mt-input
            v-model="effectiveData.reason"
            :disabled="!isEdit"
            :placeholder="$t('请输入原因说明')"
          />
        </mt-form-item>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('资质审查单')">
              <!-- @click="goto('qualificationApplyCode')" -->
              <span class="detail-href--item" @click="jumpCensorRoutes">{{
                buyerApplyEffectiveExtraDTO.qualificationApplyCode
              }}</span>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('样品认证')">
              <!-- <a
                v-for="(v,i) in fileInfoDTOMap.sample"
                :key="'a'+i"
                class="detail-href--item"
                :href="v.fileUrl"
                download
              >
                {{ v.fileName + "(" + v.approveRound+ $t("轮次") + ","+ v.approveResult + ")" }}
              </a> -->
              <p
                style="margin-top: 5px"
                v-for="(item, index) in fileInfoDTOMap.sample"
                :key="index"
              >
                <a @click="preview(item)">{{
                  item.fileName +
                  '(' +
                  item.approveRound +
                  $t('轮次') +
                  ',' +
                  item.approveResult +
                  ')'
                }}</a>
                <span style="margin-left: 10px; cursor: pointer" @click="upload(item)">{{
                  $t('下载')
                }}</span>
              </p>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('现场评审单')">
              <!-- @click="goto('reviewApplyCode')" -->
              <span class="detail-href--item" @click="jumpReviewRoutes">{{
                buyerApplyEffectiveExtraDTO.reviewApplyCode
              }}</span>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('小批量认证')">
              <!-- <a
                v-for="(v,i) in fileInfoDTOMap.smallBatch"
                :key="'a'+i"
                class="detail-href--item"
                :href="v.fileUrl"
                download
              >
                {{ v.fileName + "(" + v.approveRound+ $t("轮次") + ","  + v.approveResult + ")" }}
              </a> -->
              <p
                style="margin-top: 5px"
                v-for="(item, index) in fileInfoDTOMap.smallBatch"
                :key="index"
              >
                <a @click="preview(item)">{{
                  item.fileName +
                  '(' +
                  item.approveRound +
                  $t('轮次') +
                  ',' +
                  item.approveResult +
                  ')'
                }}</a>
                <span style="margin-left: 10px; cursor: pointer" @click="upload(item)">{{
                  $t('下载')
                }}</span>
              </p>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div>
        <!-- <mt-button type="primary" @click="addNewPurchaseOrg">{{ $t("新增") }}</mt-button>
        <mt-button type="primary" @click="delNewPurchaseOrg">{{ $t("删除") }}</mt-button>
        <mt-button type="primary" @click="submitPurchaseOrg">{{ $t("提交") }}</mt-button> -->

        <!-- <mt-button
          icon-css="mt-icons mt-icon-icon_solid_Closeorder"
          type="text"
          @click="endEdit"
          :disabled="!isEdit"
          >{{ $t('保存') }}</mt-button
        > -->

        <mt-button
          icon-css="mt-icons mt-icon-icon_solid_Createorder"
          type="text"
          @click="addNewPurchaseOrg"
          :disabled="!isEdit"
          >{{ $t('新增') }}</mt-button
        >
        <mt-button
          icon-css="mt-icons mt-icon-icon_solid_Closeorder"
          type="text"
          @click="delNewPurchaseOrg"
          :disabled="!isEdit"
          >{{ $t('删除') }}</mt-button
        >
        <mt-button
          icon-css="mt-icons mt-icon-icon_solid_Copy"
          type="text"
          @click="copyNewPurchaseOrg"
          :disabled="!isEdit"
          >{{ $t('复制') }}</mt-button
        >
      </div>
      <!-- :toolbar="['Add', 'Delete']" -->
      <mt-data-grid
        :data-source="gridDataSource"
        :column-data="gridColumnData"
        ref="dataGrid"
        :edit-settings="editSettings"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @change="chagneDataGrid"
        height="400"
      ></mt-data-grid>

      <!-- 附件上传 -->
      <div class="attachment-info" v-if="effectiveData.effectiveType == '2'">
        <div class="a-info-title">{{ $t('附件上传') }}</div>
        <mt-template-page
          ref="attachmentUploadRef"
          :template-config="attachmentConfig"
          @handleClickToolBar="handleClickToolBarEdit"
          @handleClickCellTitle="handleClickCellTitle"
          @handleClickCellTool="handleClickCellTool"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { gridColumnData, attachmentColumnData } from './config/detailGrid.js'
import utils from '@/utils/utils'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'

export default {
  data() {
    return {
      verify: false,
      effectiveData: {},
      effectiveOrgData: {},
      fileInfoDTOList: [],
      fileInfoDTOMap: {},
      buyerApplyEffectiveExtraDTO: {},
      supplierCharacterList: [
        { value: '1', text: this.$t('关联供应商') },
        { value: '2', text: this.$t('非关联供应商') }
      ],
      multiplePayConditionList: [
        { value: '1', label: this.$t('是'), text: this.$t('是') },
        { value: '0', label: this.$t('否'), text: this.$t('否') }
      ],
      offerAttributeList: [
        { value: '1', text: this.$t('寄售价') },
        { value: '2', text: this.$t('标准价') },
        { value: '3', text: this.$t('委外') }
      ],
      priceEffectiveModeList: [
        { value: '0', text: this.$t('按出库生效') },
        { value: '1', text: this.$t('按订单生效') },
        { value: '5', text: this.$t('按入库生效') }
        // { value: 'NULL', text: this.$t('按出库生效') }
      ],
      effectiveMethodMap: {
        10: this.$t('线下采委会审批'),
        20: this.$t('线上审批')
      },
      tradePartnerList: [], // 贸易伙伴列表
      paymentTermList: [], // 付款条件列表
      subjectList: [], // 统驭科目列表
      cashGroupList: [], // 先进组列表
      accountGroupList: [], // 账户组列表
      paymentModeList: [], // 付款方式列表
      subRangeList: [], // 子范围列表
      purchaseOrgList: [], // 采购组织列表
      // currencyList: [], // 币别列表
      confirmControlList: [], // 确认控制 列表
      // schemeGroupList: [], // 方案组列表
      tradeTermList: [], // 国际贸易列表
      tradeTermSecondList: [], // 用于校验 国际贸易列表第二部分 是否必填，该列表中的值都是不必填的
      dateControlList: [], // 定价日期控制列表
      drawerList: [], // 出票方列表
      orderAddressList: [], // 订货地址列表

      certificationList: [],

      dictFields: {
        value: 'itemCode',
        text: 'itemName'
      },

      formObject: {},
      // rules: {
      //   supplierCharacter: [
      //     {
      //       required: true,
      //       message: this.$t('请选择供应商特性'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   tradePartnerCode: [
      //     {
      //       required: true,
      //       message: this.$t('请选择贸易伙伴'),
      //       trigger: 'blur'
      //     },
      //     {
      //       validator: this.validateTradePartner,
      //       trigger: 'blur'
      //     }
      //   ],
      //   sapCode: [
      //     {
      //       required: true,
      //       message: this.$t('请输入SAP编码'),
      //       trigger: 'blur'
      //     },
      //     {
      //       validator: this.validateSapCode,
      //       trigger: 'blur'
      //     }
      //   ],
      //   paymentTerm: [
      //     {
      //       required: true,
      //       message: this.$t('请选择付款条件'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   subject: [
      //     {
      //       required: true,
      //       message: this.$t('请选择统驭科目'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   cashGroup: [
      //     {
      //       required: true,
      //       message: this.$t('请选择现金管理组'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   accountGroup: [{ required: true, message: this.$t('请选择账户组'), trigger: 'blur' }],
      //   paymentMode: [
      //     {
      //       required: true,
      //       message: this.$t('请选择付款方式'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   subRange: [
      //     {
      //       required: true,
      //       message: this.$t('请选择子范围'),
      //       trigger: 'blur'
      //     },
      //     { validator: this.validateSubRange, trigger: 'blur' }
      //   ]
      // },
      effectiveDTORules: {
        offerAttribute: [
          {
            required: true,
            message: this.$t('请选择报价属性'),
            trigger: 'blur'
          }
        ],
        priceEffectiveMode: [
          {
            required: true,
            message: this.$t('请选择价格生效方式'),
            trigger: 'blur'
          }
        ],
        reason: [
          {
            required: true,
            message: this.$t('请输入原因说明'),
            trigger: 'blur'
          }
        ]
      },
      gridDataSource: [],
      gridColumnData: gridColumnData,
      isDocumentNotEdit: false,
      // editSettings: {
      //   // allowEditing: true,
      //   allowAdding: true,
      //   allowDeleting: true,
      //   mode: "Normal", // 选择默认模式，双击整行可以进行编辑
      //   showConfirmDialog: false,
      //   showDeleteConfirmDialog: false,
      //   newRowPosition: "Bottom",
      // },
      attachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [['Add', 'Edit', 'Delete']]
          },
          useToolTemplate: false,
          grid: {
            height: '200px',
            columnData: attachmentColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  computed: {
    detailId() {
      return this.$route.query.id
    },

    isEdit() {
      return [10, 30, 90].includes(this.formObject?.infoDTO?.applyStatus)
      // return true
    },
    editSettings() {
      return {
        allowEditing: this.isEdit,
        allowAdding: this.isEdit,
        allowDeleting: this.isEdit,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Bottom'
      }
    },
    rules() {
      return {
        supplierCharacter: [
          {
            required: true,
            message: this.$t('请选择供应商特性'),
            trigger: 'blur'
          }
        ],
        tradePartnerCode: [
          {
            required: true,
            message: this.$t('请选择贸易伙伴'),
            trigger: 'blur'
          },
          {
            validator: this.validateTradePartner,
            trigger: 'blur'
          }
        ],
        sapCode: [
          {
            required: true,
            message: this.$t('请输入SAP编码'),
            trigger: 'blur'
          },
          {
            validator: this.validateSapCode,
            trigger: 'blur'
          }
        ],
        paymentTerm: [
          {
            required: true,
            message: this.$t('请选择付款条件'),
            trigger: 'blur'
          }
        ],
        subject: [
          {
            required: true,
            message: this.$t('请选择统驭科目'),
            trigger: 'blur'
          }
        ],
        cashGroup: [
          {
            required: !['2M01', '2S06'].includes(this.effectiveData.orgCode),
            message: this.$t('请选择现金管理组'),
            trigger: 'blur'
          }
        ],
        accountGroup: [{ required: true, message: this.$t('请选择账户组'), trigger: 'blur' }],
        paymentMode: [
          {
            required: !['2M01', '2S06'].includes(this.effectiveData.orgCode),
            message: this.$t('请选择付款方式'),
            trigger: 'blur'
          }
        ],
        subRange: [
          {
            required: true,
            message: this.$t('请选择子范围'),
            trigger: 'blur'
          },
          { validator: this.validateSubRange, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDetail(this.detailId, async (res) => {
      this.certificationList = await this.certificationdict()
      // 确认控制枚举的处理
      let confirmControlList = await this.getDictItems('CONFIRM_CONTROL')
      this.confirmControlList = confirmControlList.filter((item) => {
        if (item.itemCode === '0002' || item.itemCode === '0003') {
          return false // 删除当前项
        }
        return true // 保留其他项
      })
      this.subjectList = ['2M01', '2S06'].includes(this.effectiveData.orgCode)
        ? await this.getSubjectOptions()
        : await this.getDictItems('CONTROLLING_ACCOUNTS')
      this.cashGroupList = await this.getDictItems('CASH_GROUP')
      this.accountGroupList = ['2M01', '2S06'].includes(this.effectiveData.orgCode)
        ? await this.getDictItems('ACCOUNT_GROUP_TX')
        : await this.getDictItems('ACCOUNT_GROUP')
      this.paymentModeList = await this.getPaymentMethodByOrgId('payMethod')
      this.tradeTermList = await this.getDictItems('CONDITIONS_FOR_INCOTERMS')
      this.tradeTermSecondList = await this.getDictItems('INCOTERMS-SECONFD')

      this.dateControlList = await this.getDictItems('PRICING_DATE_CONTROL')
      this.drawerList = await this.getDictItems('DRAWER')
      this.subRangeList = await this.getDictItems('SUB_RANGE')
      // 订货地址处理
      let orderAddressList = await this.getDictItems('ORDER_ADDRESS')
      this.orderAddressList = orderAddressList.map((item) => {
        if (item.itemCode === '001' || item.itemCode === 'M1040403A') {
          return item
        }
      })

      this.paymentTermList = await this.queryPaymentTerms()
      // this.schemeGroupList = await this.querySchemaGroup();
      this.tradePartnerList = await this.queryTradePartner()
      this.purchaseOrgList = await this.queryBusinessOrg()
      // this.currencyList = await this.queryCurrency()
      this.taxTypeList = await this.queryTaxType()
      let paymentModeData = this.paymentModeList.filter((item) => {
        return item.paymethodCode == res.data.effectiveOrgDTO.paymentMode
      })
      if (paymentModeData.length < 1) {
        this.paymentModeList.splice(1, 0, {
          paymethodCode: res.data.effectiveOrgDTO.paymentMode,
          paymethodName: res.data.effectiveOrgDTO.paymentModeName
        })
      }
      this.gridDataSource = (res.data.purchaseOrgDTOList || []).map((item) => {
        item.meta = {}
        item.meta.purchaseOrgList = this.purchaseOrgList
        item.meta.confirmControlList = this.confirmControlList
        item.meta.drawerList = this.drawerList
        // item.meta.schemeGroupList = this.schemeGroupList;
        // item.meta.currencyList = this.currencyList
        item.meta.taxTypeList = this.taxTypeList
        item.meta.tradeTermList = this.tradeTermList
        item.meta.tradeTermSecondList = this.tradeTermSecondList
        item.meta.dateControlList = this.dateControlList
        item.meta.orderAddressList = this.orderAddressList
        item.meta.paymentTermList = this.paymentTermList
        item.meta.orgId = this.effectiveData?.orgId
        return item
      })
      this.attachmentConfig[0].grid.dataSource = res.data.fileInfoDTOList || []
    })
  },
  async mounted() {
    // this.getDetail(this.detailId);
  },
  methods: {
    jumpCensorRoutes() {
      if (this.verify) {
        this.$router.push({
          path:
            '/supplier/pur/qualificationInfo?applyCode=' +
            this.buyerApplyEffectiveExtraDTO.qualificationApplyCode
        })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('数据还未保存，请保存数据')
          },
          success: () => {}
        })
      }
    },
    jumpReviewRoutes() {
      if (this.verify) {
        this.$router.push({
          path:
            '/supplier/pur/review-detail?code=' + this.buyerApplyEffectiveExtraDTO.reviewApplyCode
        })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('数据还未保存，请保存数据')
          },
          success: () => {}
        })
      }
    },
    // 预览
    preview(item) {
      let params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 下载
    upload(item) {
      this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
        let link = document.createElement('a')
        link.style.display = 'none'
        let blob = new Blob([res.data], { type: 'application/x-msdownload' })
        let url = window.URL.createObjectURL(blob)
        link.href = url
        link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
        link.click() // 点击下载
        window.URL.revokeObjectURL(url)
      })
    },
    goto(type) {
      if (
        type == 'qualificationApplyCode' &&
        this.buyerApplyEffectiveExtraDTO.qualificationApplyCode
      ) {
        this.$router.push({
          path: '/supplier/pur/qualificationInfo',
          query: {
            applyCode: this.buyerApplyEffectiveExtraDTO.qualificationApplyCode
          }
        })
      }
      if (type == 'reviewApplyCode' && this.buyerApplyEffectiveExtraDTO.reviewApplyCode) {
        this.$router.push({
          path: '/supplier/pur/review-detail',
          query: {
            code: this.buyerApplyEffectiveExtraDTO.reviewApplyCode
          }
        })
      }
    },
    getDetail(id, cb) {
      if (!id) return
      this.$API.supplierEffective.getDetail(id).then((res) => {
        if (res.code === 200) {
          this.formObject = res.data
          if (res.data.effectiveDTO.priceEffectiveMode === 'NULL') {
            res.data.effectiveDTO.priceEffectiveMode = '0'
          }
          this.effectiveData = res.data.effectiveDTO || {}
          this.effectiveOrgData = {
            ...res.data.effectiveOrgDTO,
            multiplePayCondition: String(res.data.effectiveOrgDTO?.multiplePayCondition || 0)
          }
          this.buyerApplyEffectiveExtraDTO = res.data.buyerApplyEffectiveExtraDTO || {}
          if (this.formObject?.infoDTO?.applyStatus == 90) {
            this.isDocumentNotEdit = false
          } else {
            this.isDocumentNotEdit = this.effectiveOrgData.activeStatus == 1 ? true : false
          }
          this.$set(
            this.effectiveOrgData,
            'subRange',
            this.effectiveOrgData.subRangeList?.map((item) => item.subRange) || []
          )
          let arr = res.data.buyerApproveResponses || []
          this.fileInfoDTOMap = {
            sample: [],
            smallBatch: []
          }
          arr.forEach((item) => {
            let approveResult = ''
            let approveRound = item.approveRound
            this.certificationList.forEach((e) => {
              if (e.itemCode == item.approveResult) {
                approveResult = e.itemName
              }
            })
            item.buyerAuthProjectFileResponses.forEach((e) => {
              if (e.bizType == 'sample') {
                this.fileInfoDTOMap.sample.push({
                  ...e,
                  approveRound,
                  approveResult
                })
              }
              if (e.bizType == 'smallBatch') {
                this.fileInfoDTOMap.smallBatch.push({
                  ...e,
                  approveRound,
                  approveResult
                })
              }
            })
          })
          // this.fileInfoDTOList = res.data.fileInfoDTOList || [];

          // this.fileInfoDTOMap = (res.data?.fileInfoDTOList || []).reduce(
          //   (total, curr) => {
          //     total[curr.bizType] = curr;
          //     return total;
          //   },
          //   {}
          // );

          if (typeof cb == 'function') {
            cb(res)
          }
        } else {
          this.$toast({ content: res.data.msg, type: 'error' })
        }
      })
    },

    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    },

    supplierCharacterChange() {
      console.log(this.effectiveOrgData)
      // const { itemData } = event
      // if (itemData.value === '1') {
      //   // 关联供应商
      // }
    },

    tradePartnerChange(event) {
      const { itemData } = event
      console.log(this.effectiveOrgData, itemData)
      if (itemData) {
        this.effectiveOrgData.tradePartnerCode = itemData.tradingPartnerCode
        this.effectiveOrgData.tradePartnerName = itemData.tradingPartnerName
      } else {
        this.effectiveOrgData.tradePartnerCode = ''
        this.effectiveOrgData.tradePartnerName = ''
      }
    },

    paymentTermsChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.paymentTermsName = itemData.paymentTermsName
      } else {
        this.effectiveOrgData.paymentTermsName = ''
      }
    },

    subjectChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.subjectName = itemData.itemName
      } else {
        this.effectiveOrgData.subjectName = ''
      }
    },

    cashGroupChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.cashGroupName = itemData.itemName
      } else {
        this.effectiveOrgData.cashGroupName = ''
      }
    },

    accountGroupChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.accountGroupName = itemData.itemName
      } else {
        this.effectiveOrgData.accountGroupName = ''
      }
    },

    paymentModeChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.paymentModeName = itemData.itemName
      } else {
        this.effectiveOrgData.paymentModeName = ''
      }
    },

    subRangeChange(event) {
      const { value } = event
      this.$set(this.effectiveOrgData, 'subRange', value)
    },

    validateSubRange(rule, value, callback) {
      if (Array.isArray(value) && value.length < 1) {
        callback(new Error('请选择子范围'))
      } else {
        callback()
      }
    },
    validateTradePartner(rule, value, callback) {
      if (this.effectiveData.subRange === '1') {
        if (!value) {
          callback(this.$t('请选择贸易伙伴'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateSapCode(rule, value, callback) {
      if (this.effectiveData.subRange === '1') {
        if (Array.isArray(value) && value.length < 1) {
          callback(this.$t('请输入SAP编码'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    submitPurchaseOrg() {
      this.endEdit()
    },

    addNewPurchaseOrg() {
      this.endEdit()
      setTimeout(() => {
        this.$refs.dataGrid.ejsRef.addRecord({
          purchaseOrgName: '',
          purchaseOrgCode: '',
          currencyName: '',
          taxName: '',
          confirmControlName: '',
          drawerName: '',
          schemeGroup: '',
          schemeGroupName: '',
          tradeTermName: '',
          dateControlName: '',
          orderAddressName: '',
          payConditionName: '', // 付款条件
          quoteAttrName: '',
          priceEffectiveMethodName: '',
          thisApply: 1,
          meta: {
            purchaseOrgList: this.purchaseOrgList,
            confirmControlList: this.confirmControlList,
            drawerList: this.drawerList,
            // schemeGroupList: this.schemeGroupList,
            // currencyList: this.currencyList,
            taxTypeList: this.taxTypeList,
            tradeTermList: this.tradeTermList,
            dateControlList: this.dateControlList,
            orderAddressList: this.orderAddressList,
            paymentTermList: this.paymentTermList,
            orgId: this.effectiveData?.orgId
          }
        })
      }, 1000)
      // this.$nextTick(() => {
      //   this.$refs.dataGrid.ejsRef.addRecord();
      // });
    },
    delNewPurchaseOrg() {
      let _selectRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (_selectRecords.length > 1) {
        let selectRecordsStr = _selectRecords.reduce((str, item, index) => {
          if (item.activeStatus == 1) {
            return (str += `${index + 1},`)
          } else {
            return (str += '')
          }
        }, '')
        if (selectRecordsStr != '') {
          selectRecordsStr = selectRecordsStr.slice(0, -1) + this.$t('行')
          this.$toast({
            content:
              this.$t('当前选中数据中第') +
              selectRecordsStr +
              this.$t('非本单据创建的采购组织或已生效的采购组织不可删除'),
            type: 'warning'
          })
          return
        } else {
          this.$refs.dataGrid.ejsRef.deleteRecord()
        }
      } else {
        let activeStatusRecordIndex = _selectRecords.findIndex((record) => {
          return record.activeStatus == 1
        })
        if (activeStatusRecordIndex != -1) {
          this.$toast({
            content: this.$t('非本单据创建的采购组织或已生效的采购组织不可删除'),
            type: 'warning'
          })
          return
        }
        this.$refs.dataGrid.ejsRef.deleteRecord()
      }
      // let activeStatusRecordIndex = _selectRecords.findIndex(record => {
      //     return record.thisApply == 0 || record.activeStatus == 1;
      //   });
      //   if (activeStatusRecordIndex != -1) {
      //     this.$toast({ content: "非本单据创建的采购组织或已生效的采购组织不可删除", type: "warning" });
      //     return;
      //   }
      //   this.$refs.dataGrid.ejsRef.deleteRecord();
    },
    copyNewPurchaseOrg() {
      this.endEdit()
      let _selectRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      console.log('复制一条数据', _selectRecords)
      if (_selectRecords.length < 1) {
        this.$toast({
          content: this.$t('请选择一条数据'),
          type: 'warning'
        })
        return
      } else if (_selectRecords.length > 1) {
        this.$toast({
          content: this.$t('只能选择一条数据'),
          type: 'warning'
        })
        return
      } else {
        // this.$dialog({
        //   data: {
        //     title: this.$t('提示'),
        //     message: this.$t('确认复制数据？')
        //   },
        //   success: () => {
        //     console.log('_selectRecords', _selectRecords)

        //     let param = {
        //       ..._selectRecords[0]
        //     }
        //     delete param.id
        //     param.thisApply = '1'
        //     param.activeStatus = '0'
        //     param.cancel = true
        //     setTimeout(() => {
        //       this.$refs.dataGrid.ejsRef.addRecord(param)
        //     }, 1000)
        //   }
        // })

        let param = {
          ..._selectRecords[0]
        }
        delete param.id
        param.thisApply = '1'
        param.activeStatus = '0'
        param.cancel = true
        // let _gridDataSource = this.gridDataSource
        // _gridDataSource.unshift(param)
        // this.$set(this, 'gridDataSource', _gridDataSource)
        // this.$refs.dataGrid.refresh()
        // console.log('gridDataSource', this)
        setTimeout(() => {
          this.$refs.dataGrid.ejsRef.addRecord(param)
        }, 1000)
      }

      // this.$refs.dataGrid.ejsRef.deleteRecord()
    },
    endEdit() {
      this.$refs.dataGrid.ejsRef.endEdit()
    },

    chagneDataGrid(event) {
      // const { index } = event;
      // this.gridDataSource.splice(index, 1, {
      //   ...this.gridDataSource[index],
      //   ...event,
      // });
      // this.$refs.dataGrid.ejsRef.updateRow(index, event);
      console.log('chagneDataGrid', event)
    },

    actionBegin(args) {
      console.log('args', args)
      if (args.requestType === 'beginEdit') {
        // 采购组织的生效状态activeStatus（0未生效  1已生效）
        // 是否是本单据创建的采购组织thisApply（0否  1是）
        // 非本单据创建的采购组织或已生效的采购组织不可编辑或删除
        if (!args.rowData.activeStatus) {
          args.rowData.activeStatus = 0
        }
        // if (!args.rowData.thisApply) {
        //   args.rowData.thisApply = 1;
        // }
        if (args.rowData.activeStatus == 1) {
          this.$toast({
            content: this.$t('非本单据创建的采购组织或已生效的采购组织不可编辑'),
            type: 'warning'
          })
          args.cancel = true //禁止行编辑
        }
        args.rowData.orgCode = this.effectiveData.orgCode
      }
    },

    actionComplete(event) {
      const { action, requestType } = event
      if (action === 'edit' && requestType === 'save') {
        console.log('this.gridDataSource', this.gridDataSource)
        this.gridDataSource = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
      }
    },

    backDetail() {
      this.$router.go(-1)
    },

    saveAndSubmitDetail() {
      this.saveDetailToServe()
        .then(() => {
          this.submitDetail()
        })
        .catch(() => {
          this.$hloading()
        })
    },

    saveDetail() {
      // this.endEdit()
      this.saveDetailToServe()
        .then((res) => {
          this.$hloading()
          this.$toast({
            content: res,
            type: 'success'
          })
          this.verify = true
          // // 刷新页面
          location.reload()
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err || this.$t('保存失败'),
            type: 'warning'
          })
        })
    },

    saveDetailToServe() {
      return new Promise((resolve, reject) => {
        const effectiveDTO = new Promise((resolve, reject) => {
          this.$refs.effectiveDTO.validate((valid) => {
            valid ? resolve(valid) : reject(valid)
          })
        })

        const effectiveOrgDTO = new Promise((resolve, reject) => {
          this.$refs.effectiveOrgDTO.validate((valid) => {
            valid ? resolve(valid) : reject(valid)
          })
        })

        const purchaseOrgDTOList = new Promise((resolve, reject) => {
          const tipMap = new Map([
            ['purchaseOrgCode', '采购组织不能为空'],
            ['currency', '币种不能为空'],
            ['tax', '税码不能为空'],
            ['transferFactory', '是否转厂不能为空'],
            ['payCondition', '付款条件不能为空'],
            ['quoteAttr', '报价属性属性不能为空'],
            ['priceEffectiveMethod', '价格生效方式不能为空'],
            ['drawer', '出票方不能为空'],
            ['schemeGroup', '方案组不能为空']
          ])

          if (this.gridDataSource && this.gridDataSource.length) {
            let errorMsg = ''

            // tradeTermSecond

            // 判断map中的几个字段
            this.gridDataSource.some((item, i) => {
              const itemData = [...tipMap.entries()].find(([key]) => {
                return item[key] === null || item[key] === undefined || item[key] === ''
              })

              if (itemData) {
                errorMsg = `第${i + 1}行中的${tipMap.get(itemData[0])}`
              }

              // 国际贸易条件在选择某些值的时候，国际贸易条件（部分2） 该列则必填，
              // tradeTermSecondList 中存储的值是不必填的那些
              if (
                !errorMsg &&
                item['tradeTerm'] &&
                !item['tradeTermSecond'] &&
                this.tradeTermSecondList.every((t) => t.itemCode !== item['tradeTerm'])
              ) {
                errorMsg = `第${i + 1}行中的国际贸易条件（部分2）不能为空`
              }

              return !!errorMsg
            })

            if (errorMsg) {
              this.$toast({
                type: 'warning',
                content: errorMsg
              })
              reject()
            } else {
              resolve()
            }
          } else {
            this.$toast({
              type: 'warning',
              content: this.$t('采购组织列表不能为空')
            })
            reject()
          }
        })
        const fileInfoDTOList = new Promise((resolve, reject) => {
          if (
            this.effectiveData.effectiveMethod === 10 &&
            this.attachmentConfig[0].grid.dataSource.length === 0 &&
            this.effectiveData.effectiveType == '2' // 如果当前是正式生效的单据则显示附件上传的表格，并且校验是否为空
          ) {
            this.$toast({
              type: 'warning',
              content: this.$t('线下采委会审批，请上传相关附件信息')
            })
            reject()
          } else {
            resolve()
          }
        })

        Promise.all([effectiveDTO, effectiveOrgDTO, purchaseOrgDTOList, fileInfoDTOList]).then(
          () => {
            const param = this.formatDetailParam()
            this.$loading()
            this.$API.supplierEffective.updateEffective(param).then((res) => {
              if (res.code === 200 && !utils.isEmpty(res.data)) {
                resolve(res.msg)
              } else {
                reject(res.msg)
              }
            })
          }
        )
      })
    },

    submitDetail() {
      // this.$API.SupplyPunishment.queryStartFlow

      this.$API.SupplierPunishment.applySubmit({
        applyIdList: [this.detailId]
      })
        .then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.verify = true
            setTimeout(() => {
              this.backDetail()
            }, 600)
          } else {
            this.$toast({
              content: res.msg,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },

    // 付款条件接口
    queryPaymentTerms() {
      return this.$API.supplierEffective.queryPaymentTerms({}).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          return res.data || []
        }
      })
    },

    // 采购组织接口
    queryBusinessOrg() {
      return this.$API.supplierEffective
        .queryBusinessOrg({ organizationId: this.effectiveData?.orgId })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            return res.data || []
          }
        })
    },

    // 贸易伙伴
    queryTradePartner() {
      return this.$API.supplierEffective.queryTradePartner({}).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          return res.data || []
        }
      })
    },

    // 货币种类
    // queryCurrency() {
    //   return this.$API.supplierEffective.queryCurrency({ currencyCode: 'CNY' }).then((res) => {
    //     if (res.code === 200 && !utils.isEmpty(res.data)) {
    //       return res.data || []
    //     }
    //   })
    // },

    // 税码种类
    queryTaxType() {
      return this.$API.supplierEffective.queryTaxType({}).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          if (this.effectiveData && this.effectiveData.dictName === this.$t('空调公司')) {
            return (
              res.data.filter((i) => {
                return i.taxItemCode.startsWith('c') || i.taxItemCode.startsWith('C')
              }) || []
            )
          } else {
            return res.data || []
          }
        }
      })
    },
    certificationdict() {
      return this.$API.CategoryCertification.approvedict({
        dictCode: 'approveResultType'
      }).then((res) => {
        if (res.code == 200) {
          return res.data
        }
      })
    },

    getSubjectOptions() {
      return this.$API.supplierEffective
        .getTxSubjectApi({ orgCode: this.effectiveData.orgCode })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            return res.data.map((item) => {
              return {
                itemCode: item.controllingAccountCode,
                itemName: item.controllingAccountDesc
              }
            })
          }
        })
    },

    getDictItems(key) {
      return this.$API.supplierInvitation
        .getDictCode({
          dictCode: key,
          nameLike: ''
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            return res.data
          }
        })
    },
    // 查询指定组织付款方式（付款方式接口变成单独主数据那边儿的新接口请求）
    getPaymentMethodByOrgId() {
      return this.$API.supplierInvitation
        .getPaymentMethodByOrgId({
          orgId: this.effectiveData?.orgId
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            return res.data
          }
        })
    },
    formatDetailParam() {
      this.effectiveOrgData.subRangeList = []
      this.effectiveOrgData?.subRange?.forEach((itemCode) => {
        this.effectiveOrgData.subRangeList.push({ subRange: itemCode })
      })
      // delete this.effectiveOrgData.subRange;
      let purchaseOrgDTOList =
        this.gridDataSource?.map((orgDTO) => {
          let newOrgDIO = JSON.parse(JSON.stringify(orgDTO))
          delete newOrgDIO.meta
          return newOrgDIO
        }) || []
      return {
        effectiveDTO: this.effectiveData,
        effectiveOrgDTO: {
          ...this.effectiveOrgData,
          multiplePayCondition: Number(this.effectiveOrgData.multiplePayCondition)
        },
        infoDTO: this.formObject.infoDTO,
        purchaseOrgDTOList: purchaseOrgDTOList,
        fileInfoDTOList: this.attachmentConfig[0].grid.dataSource
      }
    },
    // 附件相关操作（增加、编辑、删除）
    handleClickToolBarEdit(args) {
      if (!this.isEdit) {
        this.$toast({ content: this.$t('非编辑状态，禁止操作'), type: 'warning' })
        return
      }
      const { toolbar, grid } = args
      const sltList = grid.getSelectedRecords() || []
      const selectedRowIndexes = grid.getSelectedRowIndexes()
      if (['Edit', 'Delete'].includes(toolbar.id) && sltList.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./components/uploadDialog.vue'),
          data: {
            title: this.$t('附件上传')
          },
          success: (res) => {
            const {
              fileId,
              fileName,
              fileSize,
              fileType,
              fileUrl,
              url,
              uploadUserName,
              uploadTime,
              remark
            } = res
            this.attachmentConfig[0].grid.dataSource.push({
              fileId,
              fileName,
              fileSize,
              fileType,
              fileUrl,
              url,
              createUserName: uploadUserName,
              createDate: dayjs(uploadTime).format('YYYY-MM-DD HH:mm:ss'),
              remark,
              deleteId: Date.now() // 删除时候用于查找到对应的数据
            })
          }
        })
      } else if (toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        const { fileName, fileSize, url } = sltList[0]
        this.$dialog({
          modal: () => import('./components/uploadDialog.vue'),
          data: {
            title: this.$t('附件上传'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName,
              fileSize,
              url
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            console.log('data', data)
            const _tempData = cloneDeep(this.attachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.attachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.attachmentUploadRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id == 'Delete') {
        const _tempData = cloneDeep(this.attachmentConfig[0].grid.dataSource)
        sltList.forEach((item) => {
          const index = _tempData.findIndex(
            (row) =>
              (item.id && row.id === item.id) || (item.deleteId && row.deleteId === item.deleteId)
          )
          _tempData.splice(index, 1)
        })
        this.$set(this.attachmentConfig[0].grid, 'dataSource', _tempData)
        this.$refs.attachmentUploadRef.refreshCurrentGridData()
      }
    },
    handleClickCellTitle(e) {
      // 附件预览
      if (e.field === 'fileName') {
        const params = {
          id: e.data.fileId,
          useType: 1
        }
        this.$API.SupplierPunishment.filepreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      // 附件下载
      if (tool.id == 'download') {
        this.$API.fileService
          .downloadPublicFile({
            id: data.fileId
          })
          .then((res) => {
            utils.download({
              fileName: data.fileName,
              blob: res.data
            })
          })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-effective-apply {
  background: #fafafa;
  height: 100%;

  .detail-header--wrap {
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px 30px;
    margin-bottom: 16px;

    .detail-header-name {
      font-size: 20px;
      line-height: 32px;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }
    .detail-header-category {
      font-size: 12px;
      line-height: 16px;
      color: rgba(41, 41, 41, 1);
    }
    .detail-header-items {
      font-size: 14px;
      font-weight: 600;
      margin-top: 20px;
      color: rgba(41, 41, 41, 1);
      .detail-header-item {
        margin-right: 24px;
      }
    }
  }

  .detail-content--wrap {
    padding: 24px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px 8px 0 0 0;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06);
  }

  .detail-effectiveorg--form {
    padding-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 40px;
  }

  .effective-form-item--width {
  }

  .detail-header-button--wrap {
    float: right;
    .detail-header-button {
      margin-right: 24px;
    }
  }

  .detail-href--item {
    color: #00469c;
    margin-top: 12px;
    display: block;
    cursor: pointer;
  }

  .attachment-info {
    width: 100%;
    margin-top: 20px;
    .a-info-title {
      position: relative;
      width: 100%;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }

  /deep/.e-multiselect.e-checkbox .e-multi-select-wrapper .e-searcher {
    width: 100%;
  }
}
</style>
