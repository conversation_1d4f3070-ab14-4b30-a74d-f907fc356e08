<template>
  <div class="detail-grid-cell-template" ref="templateRef">
    <!-- <mt-select
      id="purchaseOrgId"
      v-if="field === 'purchaseOrgId'"
      v-model="data.purchaseOrgId"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="e => filteringResource(e, data.meta.purchaseOrgList, 'organizationName')"
      :data-source="purchaseOrgListarr"
      :fields="{
        value: 'id',
        text: 'organizationName',
      }"
      @change="purchaseOrgChange"
    ></mt-select> -->
    <mt-input style="display: none" :id="data.column.field" v-model="data[data.column.field]" />
    <RemoteAutocomplete
      v-if="field === 'purchaseOrgCode'"
      id="purchaseOrgCode"
      method="get"
      records-position="data"
      :url="$API.masterData.getBusinessOrganizationUrl"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="false"
      v-model="data[data.column.field]"
      :params="{
        organizationId: data.meta.orgId
      }"
      :fields="{
        value: 'organizationCode',
        text: 'organizationName'
      }"
      @change="purchaseOrgChange"
      :title-switch="false"
      :placeholder="$t('请选择采购组织')"
      select-type="businessOrganization"
    ></RemoteAutocomplete>
    <!--
      货币
      :filtering="getDatalist"
      :filtering="(e) => filteringResource(e, data.meta.currencyList, 'currencyName')"
     -->
    <mt-select
      v-if="field === 'currency'"
      id="currency"
      v-model="data.currency"
      float-label-type="Never"
      :allow-filtering="true"
      :fields="{
        value: 'currencyCode',
        text: 'currencyName'
      }"
      :data-source="currencyListArr"
      @change="currencyChange"
    ></mt-select>
    <!-- <mt-input style="display: none" :id="data.column.field" v-model="data[data.column.field]" />
    <MasterdataDropdownSelects
      v-if="field === 'currency'"
      id="currency"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="false"
      v-model="data[data.column.field]"
      :fields="{
        value: 'currencyCode',
        text: 'currencyName'
      }"
      @change="currencyChange"
      :title-switch="false"
      :placeholder="$t('请选择货币')"
      select-type="money"
    ></MasterdataDropdownSelects> -->
    <mt-select
      v-if="field === 'tax'"
      id="tax"
      v-model="data.tax"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, data.meta.taxTypeList, 'taxItemName')"
      :fields="{
        value: 'taxItemCode',
        text: 'taxItemName'
      }"
      :data-source="taxTypeListArr"
      @change="taxChange"
    ></mt-select>
    <!-- <mt-input style="display: none" :id="data.column.field" v-model="data[data.column.field]" />
    <MasterdataDropdownSelects
      v-if="field === 'tax'"
      id="tax"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="false"
      v-model="data[data.column.field]"
      :fields="{
        value: 'taxItemCode',
        text: 'taxItemName'
      }"
      @change="taxChange"
      :title-switch="false"
      :placeholder="$t('请选择税码')"
      select-type="taxRate"
      :splice-title="
        (item) => {
          return `${item.taxItemCode} ${item.taxItemName}`
        }
      "
      :splice-input="
        (item) => {
          return `${item.taxItemCode} ${item.taxItemName}`
        }
      "
    ></MasterdataDropdownSelects> -->
    <mt-select
      v-if="field === 'transferFactory'"
      id="transferFactory"
      v-model="data.transferFactory"
      float-label-type="Never"
      :data-source="transferFactoryList"
      @change="transferFactoryChange"
    ></mt-select>

    <mt-select
      v-if="field === 'confirmControl'"
      id="confirmControl"
      v-model="data.confirmControl"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, data.meta.confirmControlList, 'itemName')"
      :data-source="data.meta.confirmControlList"
      :fields="dictFields"
      @change="confirmControlChange"
    ></mt-select>

    <mt-select
      v-if="field === 'drawer'"
      id="drawer"
      v-model="data.drawer"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, data.meta.drawerList, 'itemName')"
      :data-source="data.meta.drawerList"
      :fields="dictFields"
      @change="drawerChange"
    ></mt-select>

    <!-- <mt-select
      v-if="field === 'schemeGroup'"
      id="schemeGroup"
      v-model="data.schemeGroup"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filterSchema(e)"
      :data-source="data.schemaList"
      :fields="{
        value: 'schemaGroupCode',
        text: 'schemaGroupName'
      }"
      @change="schemeGroupChange"
    ></mt-select> -->
    <mt-input style="display: none" :id="data.column.field" v-model="data[data.column.field]" />
    <!-- <RemoteAutocomplete
      v-if="field === 'schemeGroup'"
      id="schemeGroup"
      records-position="data"
      :url="$API.masterData.getSchemaGroupUrl"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="false"
      v-model="data[data.column.field]"
      :fields="{
        value: 'schemaGroupCode',
        text: 'schemaGroupName'
      }"
      :params="{
        businessOrganizationCode: data.purchaseOrgCode
      }"
      @change="schemeGroupChange"
      :title-switch="false"
      :placeholder="$t('请选择方案组')"
      select-type="schemaGroup"
    ></RemoteAutocomplete> -->
    <!-- <mt-select
      v-if="field === 'tradeTerm'"
      id="tradeTerm"
      v-model="data.tradeTerm"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="e => filteringResource(e, data.meta.tradeTermList, 'itemName')"
      :data-source="tradeTermListArr"
      :fields="dictFields"
      @change="tradeTermChange"
    ></mt-select> -->
    <mt-input style="display: none" :id="data.column.field" v-model="data[data.column.field]" />
    <RemoteAutocomplete
      v-if="field === 'tradeTerm'"
      url="/masterDataManagement/tenant/dict-item/dict-code"
      id="tradeTerm"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="false"
      v-model="data[data.column.field]"
      :fields="{
        value: 'itemCode',
        text: 'itemName'
      }"
      @change="tradeTermChange"
      :title-switch="false"
      :params="{
        dictCode: 'CONDITIONS_FOR_INCOTERMS'
      }"
      :placeholder="$t('请选择国际贸易条件')"
      records-position="data"
      select-type="dictItem"
    ></RemoteAutocomplete>
    <mt-input
      id="tradeTermSecond"
      v-if="field === 'tradeTermSecond'"
      v-model="data.tradeTermSecond"
      :disabled="isDisabledTradeTermSecond"
    ></mt-input>

    <mt-select
      v-if="field === 'dateControl'"
      id="dateControl"
      v-model="data.dateControl"
      float-label-type="Never"
      :allow-filtering="true"
      :data-source="data.meta.dateControlList"
      :fields="dictFields"
      @change="dateControlChange"
    ></mt-select>

    <mt-select
      v-if="field === 'orderAddress'"
      id="orderAddress"
      v-model="data.orderAddress"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, data.meta.orderAddressList, 'itemName')"
      :data-source="data.meta.orderAddressList"
      :fields="dictFields"
      @change="orderAddressChange"
    ></mt-select>

    <!-- 付款条件 -->
    <mt-select
      v-if="field === 'payCondition'"
      id="payCondition"
      v-model="data.payCondition"
      float-label-type="Never"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, data.meta.paymentTermList, 'paymentTermsName')"
      :data-source="data.meta.paymentTermList"
      :fields="{
        value: 'paymentTermsCode',
        text: 'paymentTermsName'
      }"
      @change="paymentModeChange"
    ></mt-select>

    <!-- 报价属性 -->
    <mt-select
      v-if="field === 'quoteAttr'"
      id="quoteAttr"
      v-model="data.quoteAttr"
      float-label-type="Never"
      :allow-filtering="true"
      :data-source="quoteAttributeList"
      :fields="{ value: 'value', text: 'text' }"
      @change="(e) => handleSelectValueChange(e, 'quoteAttrName')"
    />
    <!-- 价格生效方式 -->
    <mt-select
      v-if="field === 'priceEffectiveMethod'"
      id="priceEffectiveMethod"
      v-model="data.priceEffectiveMethod"
      float-label-type="Never"
      :allow-filtering="true"
      :data-source="priceEffectiveMethodList"
      :fields="{ value: 'value', text: 'text' }"
      @change="(e) => handleSelectValueChange(e, 'priceEffectiveMethodName')"
    />

    <div v-if="field === 'activeStatus'">
      {{ data.activeStatus == 0 ? $t('未生效') : $t('已生效') }}
    </div>
  </div>
</template>

<script>
import bus from '../config/bus'
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  data() {
    return {
      transferFactoryList: [
        { value: 0, text: this.$t('否') },
        { value: 1, text: this.$t('是') }
      ],
      dictFields: {
        value: 'itemCode',
        text: 'itemName'
      },
      isDisabledTradeTermSecond: true,
      schemeGroupList: [],
      purchaseOrgListarr: [], //采购组织
      currencyListArr: [], //币别
      taxTypeListArr: [], //税码
      tradeTermListArr: [], //国际贸易条件
      paymentTermList: [], // 付款条件
      purchaseOrgCode: null,
      quoteAttributeList: [
        { value: '1', text: this.$t('寄售价') },
        { value: '2', text: this.$t('标准价') },
        { value: '3', text: this.$t('委外') }
      ],
      priceEffectiveMethodList: [
        { value: '0', text: this.$t('按出库生效') },
        { value: '1', text: this.$t('按订单生效') },
        { value: '5', text: this.$t('按入库生效') }
      ]
    }
  },
  computed: {
    field() {
      return this.data.column.field
    }
  },
  mounted() {
    console.log('this.data.meta', this.data.meta, this.data)
    //采购组织
    if (this.field === 'purchaseOrgId') {
      this.data.meta.purchaseOrgList.map((item) => {
        item.organizationName = item.organizationCode + '-' + item.organizationName
      })
      this.purchaseOrgListarr = this.data.meta.purchaseOrgList
    }
    //币别
    if (this.field === 'currency') {
      if (['2M01', '2S06'].includes(this.data.orgCode)) {
        this.getCurrencyOptions()
      } else {
        this.queryCurrency()
      }
    }
    //税码
    if (this.field === 'tax') {
      this.data.meta.taxTypeList.map((item) => {
        item.taxItemName = item.taxItemCode + '-' + item.taxItemName
      })
      this.taxTypeListArr = this.data.meta.taxTypeList
    }
    //国际贸易条件
    if (this.field === 'tradeTerm') {
      this.data.meta.tradeTermList.map((item) => {
        item.itemName = item.itemCode + '-' + item.itemName
      })
      this.tradeTermListArr = this.data.meta.tradeTermList
    }
    if (this.field === 'tradeTermSecond') {
      this.changeData('tradeTermName', { itemCode: this.data.tradeTerm })
      bus.$on('onChangeTradeTerm', this.changeData)
    }
    if (this.field === 'schemeGroup') {
      // this.querySchemaGroup(this.data.purchaseOrgCode);
      // bus.$on("onChangePurchaseOrg", this.changeData);
      bus.$on('onChangePurchaseOrgCode', this.changeData)
      // bus.$on('onChangeSchemeGroup', this.changeData)
    }
    // this.schemeGroupList = await this.querySchemaGroup();
  },
  methods: {
    // 方案组接口
    querySchemaGroup(value) {
      this.$API.supplierEffective
        .querySchemaGroup({
          businessOrganizationCode: value
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            res.data.map((item) => {
              item.schemaGroupName = item.schemaGroupCode + '-' + item.schemaGroupName
            })
            this.schemeGroupList = res.data || []
            this.$set(this.data, 'schemaList', res.data)
            bus.$emit('changeCell', 'schemaList', res.data)
            console.log('onChangePurchaseOrgCodeonChangePurchaseOrgCode', this.data.schemaList)
          }
        })
    },
    //货币接口
    queryCurrency() {
      this.$API.supplierEffective.queryCurrency({}).then((res) => {
        let _data = cloneDeep(res.data)
        _data.map((item, index) => {
          item.currencyName = item.currencyCode + '-' + item.currencyName
          if (item.currencyCode === 'CNY') {
            _data.splice(index, 1)
            _data.splice(0, 0, item)
          }
        })
        this.currencyListArr = _data
      })
    },
    getCurrencyOptions() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'TX3BU-Currency'
        })
        .then((res) => {
          if (res.code === 200) {
            this.currencyListArr = res.data.map((item) => {
              return {
                currencyName: item.itemCode + '-' + item.itemName,
                currencyCode: item.itemCode
              }
            })
          }
        })
    },
    changeData(field, itemData) {
      if (field === 'tradeTermName') {
        const tradeTermSecondList = this.data?.meta?.tradeTermSecondList || []

        if (tradeTermSecondList.every((t) => t.itemCode !== itemData.itemCode)) {
          this.isDisabledTradeTermSecond = false
        } else {
          this.isDisabledTradeTermSecond = true
          this.data.tradeTermSecond = ''
        }
      } else if (field === 'purchaseOrgCode') {
        this.data.purchaseOrgCode = itemData
      } else if (field === 'schemeGroup') {
        this.data.schemeGroup = ''
      }
      // this.data.schemeGroup = "";
      // } else if (field === "purchaseOrgId") {
      //   // this.data.schemeGroup = "";
      //   // this.schemeGroupList = [];
      //   // this.querySchemaGroup(itemData.organizationCode);
      // }
    },

    purchaseOrgChange(event) {
      console.log('purchaseOrgChangepurchaseOrgChange', event.itemData.organizationCode)
      this.purchaseOrgCode = event.itemData.organizationCode

      const { itemData } = event
      if (JSON.stringify(itemData) === JSON.stringify({})) {
        // console.log('8989898', 1111111)
        return
      }
      if (itemData.organizationName == this.data.purchaseOrgName) {
        return
      }
      bus.$emit('changeExt', 'schemeGroupName', '')
      // if (event.e !== null) {
      bus.$emit('onChangeSchemeGroup', 'schemeGroup', '')
      // }
      bus.$emit('changeExt', 'purchaseOrgName', itemData.organizationName)
      bus.$emit('changeExt', 'purchaseOrgCode', itemData.organizationCode)
      bus.$emit('changeExt', 'purchaseOrgId', itemData.id)
      bus.$emit('onChangePurchaseOrg', 'purchaseOrgId', itemData.id)
      bus.$emit('onChangePurchaseOrgCode', 'purchaseOrgCode', itemData.organizationCode)
      this.querySchemaGroup(itemData.organizationCode)
    },

    currencyChange(event) {
      const { itemData } = event
      if (itemData.currencyName == this.data.currencyName) {
        return
      }
      bus.$emit('changeExt', 'currencyName', itemData.currencyName)
    },

    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    },
    taxChange(event) {
      const { itemData } = event
      if (itemData.taxName == this.data.taxName) {
        return
      }
      bus.$emit('changeExt', 'taxName', itemData.taxItemName)
    },

    transferFactoryChange() {},

    confirmControlChange(event) {
      const { itemData } = event
      if (itemData.confirmControlName == this.data.confirmControlName) {
        return
      }
      bus.$emit('changeExt', 'confirmControlName', itemData.itemName)
    },

    drawerChange(event) {
      const { itemData } = event
      if (itemData.drawerName == this.data.drawerName) {
        return
      }
      bus.$emit('changeExt', 'drawerName', itemData.itemName)
    },

    schemeGroupChange(event) {
      const { itemData } = event
      if (JSON.stringify(itemData) == JSON.stringify({})) {
        return
      }
      if (itemData.schemaGroupName == undefined) {
        bus.$emit('changeExt', 'schemeGroupName', '')
        return
      }
      bus.$emit('changeExt', 'schemeGroupName', itemData.schemaGroupName)
    },

    tradeTermChange(event) {
      const { itemData } = event
      if (itemData.tradeTermName == this.data.tradeTermName) {
        return
      }
      bus.$emit('changeExt', 'tradeTermName', itemData.itemName)
      bus.$emit('onChangeTradeTerm', 'tradeTermName', itemData)
    },
    dateControlChange(event) {
      const { itemData } = event
      if (itemData.dateControlName == this.data.dateControlName) {
        return
      }
      bus.$emit('changeExt', 'dateControlName', itemData.itemName)
    },

    orderAddressChange(event) {
      const { itemData } = event
      if (itemData.orderAddressName == this.data.orderAddressName) {
        return
      }
      bus.$emit('changeExt', 'orderAddressName', itemData.itemName)
    },
    paymentModeChange(event) {
      const { itemData } = event
      if (itemData.paymentTermsName == this.data.payConditionName) {
        return
      }
      bus.$emit('changeExt', 'payConditionName', itemData.paymentTermsName)
    },
    // 下拉选择触发
    handleSelectValueChange(event, fieldName, valueKey = 'text') {
      const { itemData } = event
      if (itemData[valueKey] === this.data[fieldName]) {
        return
      }
      bus.$emit('changeExt', fieldName, itemData[valueKey])
    }
  }
}
</script>

<style lang="scss" scoped></style>
