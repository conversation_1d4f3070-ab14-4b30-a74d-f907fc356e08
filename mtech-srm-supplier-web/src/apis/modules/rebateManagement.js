import { API } from '@mtech-common/http'
import qs from 'qs'
const NAME = 'rebateManagement'
const PROXY_BASE = '/analysis/tenant'
const PROXY_MASTER = '/masterDataManagement'

const APIS = {
  /*
    返利管理
  */

  /******** 采方-返利协议模板 *********/
  // removeHeader: (id) => {
  //   return API.post(`${PROXY_BASE}/idleMaterialVoucher/removeHeader?id=${id}`)
  // },
  // 返利协议模板-添加-编辑 -保存
  saveRebateTemplate: (data) => {
    return API.post(`${PROXY_BASE}/rebateAgreementTemplate/saveTemplate`, data)
  },
  // 返利协议模板启用-停用
  updateRebateTemplateStatus: (data) => {
    return API.post(`${PROXY_BASE}/rebateAgreementTemplate/updateTemplateStatus`, data)
  },
  // 删除返利协议模板
  deleteRebateTemplate: (data) => {
    return API.post(`${PROXY_BASE}/rebateAgreementTemplate/deleteTemplate`, data)
  },

  /******** 采方-返利协议创建 *********/
  // 列表-查询操作记录
  queryOperateRecords: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/queryOperateRecord`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 列表-删除
  deleteRebateAgreementList: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/removeVoucher`, data),
  // 列表-提交
  submitRebateAgreement: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/submit`, data)
  },
  // 列表-发布
  publishRebateAgreement: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/release`, data)
  },
  // 列表-导出
  exportRebateAgreementList: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/exportHeader`, data, {
      responseType: 'blob'
    }),
  // 列表、明细表头-打印
  printRebateAgreement: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/agreementPrint`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    })
  },
  // 明细头部-协议模板下拉列表
  queryAgreemenTempalteList: (data) => {
    return API.post(`${PROXY_BASE}/rebateAgreementTemplate/queryTemplate`, data)
  },
  // 明细头部-新增保存
  createRebateAgreement: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/createVoucher`, data)
  },
  // 明细头部-编辑保存
  saveRebateAgreement: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/updateVoucher`, data)
  },
  // 明细-根据id获取明细信息
  getRebateAgreementDetailById: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/getDetail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 明细列表-导入模板
  downloadRebateAgreementDetailTemplate: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/downloadExcelTemplate`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }),
  // 明细列表-导入
  importRebateAgreementDetailList: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/importItemList`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }),
  // 明细列表-导出
  exportRebateAgreementDetailList: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/exportItemList`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }),
  // 明细列表-根据物料编码查询物料类别
  getCategoryListByMaterial: (data = {}) =>
    API.post(`${PROXY_MASTER}/tenant/category-item/queryByItems`, data),

  // 附件-更新
  updatePurAttachmentsList: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/updateVoucherFile`, data),

  // 采方审核返利协议红字开票数据和附件
  purchaseAuditRedInvoice: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/purchaseAuditRedInvoice`, data),

  /******** 采方-返利金额计算*********/
  // 金额计算明细-根据id获取明细信息
  getRebateAmountItemDetailById: (data) => {
    return API.get(`${PROXY_BASE}/rebateAmountItem/queryDetail/${data.id}/${data.queryType}`)
  },
  // 返利金额明细-重新计算
  reCalculateRebateAmountItemDetailApi: (data) => {
    return API.post(`${PROXY_BASE}/rebateAmountItem/pvRecalculate/${data.rebateAmountItemId}`, data)
  },
  // 返利金额明细-导出
  exportRebateAmountItemDetailApi: (data) =>
    API.post(`${PROXY_BASE}/rebateAmountItem/exportAmountItem/${data.rebateHeaderId}`, {}, {
      responseType: 'blob'
    }),
  // 返利金额计算-提交OA审批
  submitRebateAmountItemDetailApi: (data) => {
    return API.post(`${PROXY_BASE}/rebateAmountItem/submitOA/${data.rebateAmountItemId}`, data)
  },
  // 返利金额计算-查看金额明细审批记录
  checkRebateAmountItemDetailApi: (data) => {
    return API.get(`${PROXY_BASE}/rebateAmountItem/getOALink/${data.rebateHeaderId}/${data.rounds}`, data)
  },
  // 金额计算明细 - 采方附件保存
  rebateAmountSaveItem: (data) => {
    return API.post(`${PROXY_BASE}/rebateAmountItem/saveItem`, data)
  },

  /******** 采方-返利基础设置*********/
  // 返利基础设置-更新启用停用状态
  updateStatus: (data) => {
    return API.post(`${PROXY_BASE}/siteProfitCenterRelation/updateStatus`, data)
  },
  // 返利基础设置 - 新增或更新
  rebateAddOrUpdate: (data) => {
    return API.post(`${PROXY_BASE}/siteProfitCenterRelation/addOrUpdate`, data)
  },
  // 返利基础设置-删除
  deleteRebateBaseSetup: (data = {}) =>
    API.post(`${PROXY_BASE}/siteProfitCenterRelation/delete`, data),
  // 返利金额计算 - 推送财务共享
  pushToFSSC: (data) => {
    return API.post(`${PROXY_BASE}/rebateAmountItem/pushToFSSC`, data)
  },
  /******** 采方-返利报表*********/
  // 返利报表-返利详情
  getByRecordId: (data = {}) => {
    return API.get(`${PROXY_BASE}/rebateRecordItem/getByRecordId`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 列表-导出
  exportrRebateRecord: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateRecord/exportData`, data, {
      responseType: 'blob'
    }),
  // 获取OA链接
  getOALink: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/getRebateReconciliationOALink`, data),
  // 对账申请
  reconciliationApply: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/reconciliationApply`, data)
}

export default {
  NAME,
  APIS
}
